<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.secondary.mapper.ProductMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.secondary.domain.Product">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="stock" jdbcType="INTEGER" property="stock"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, product_code, product_name, description, price, stock, category, status, create_time, update_time
    </sql>

    <!-- 根据ID查询产品 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM products
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据产品编码查询产品 -->
    <select id="selectByProductCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM products
        WHERE product_code = #{productCode,jdbcType=VARCHAR}
    </select>

    <!-- 查询所有产品 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM products
        ORDER BY create_time DESC
    </select>

    <!-- 根据分类查询产品 -->
    <select id="selectByCategory" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM products
        WHERE category = #{category,jdbcType=VARCHAR}
        ORDER BY create_time DESC
    </select>

    <!-- 根据状态查询产品 -->
    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM products
        WHERE status = #{status,jdbcType=VARCHAR}
        ORDER BY create_time DESC
    </select>

    <!-- 根据价格范围查询产品 -->
    <select id="selectByPriceRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM products
        WHERE 1=1
        <if test="minPrice != null">
            AND price &gt;= #{minPrice,jdbcType=DECIMAL}
        </if>
        <if test="maxPrice != null">
            AND price &lt;= #{maxPrice,jdbcType=DECIMAL}
        </if>
        ORDER BY price ASC
    </select>

    <!-- 根据产品名称模糊查询 -->
    <select id="selectByNameLike" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM products
        WHERE product_name LIKE CONCAT('%', #{productName,jdbcType=VARCHAR}, '%')
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询产品 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM products
        ORDER BY create_time DESC
        LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <!-- 统计产品总数 -->
    <select id="countAll" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM products
    </select>

    <!-- 根据分类统计产品数 -->
    <select id="countByCategory" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM products
        WHERE category = #{category,jdbcType=VARCHAR}
    </select>

    <!-- 根据状态统计产品数 -->
    <select id="countByStatus" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM products
        WHERE status = #{status,jdbcType=VARCHAR}
    </select>

    <!-- 插入产品 -->
    <insert id="insert" parameterType="com.example.secondary.domain.Product" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO products (product_code, product_name, description, price, stock, category, status, create_time, update_time)
        VALUES (#{productCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
                #{price,jdbcType=DECIMAL}, #{stock,jdbcType=INTEGER}, #{category,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入产品 -->
    <insert id="insertSelective" parameterType="com.example.secondary.domain.Product" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO products
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productCode != null">product_code,</if>
            <if test="productName != null">product_name,</if>
            <if test="description != null">description,</if>
            <if test="price != null">price,</if>
            <if test="stock != null">stock,</if>
            <if test="category != null">category,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
            <if test="productName != null">#{productName,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="price != null">#{price,jdbcType=DECIMAL},</if>
            <if test="stock != null">#{stock,jdbcType=INTEGER},</if>
            <if test="category != null">#{category,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!-- 根据ID更新产品 -->
    <update id="updateById" parameterType="com.example.secondary.domain.Product">
        UPDATE products
        SET product_code = #{productCode,jdbcType=VARCHAR},
            product_name = #{productName,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            price = #{price,jdbcType=DECIMAL},
            stock = #{stock,jdbcType=INTEGER},
            category = #{category,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 选择性更新产品 -->
    <update id="updateByIdSelective" parameterType="com.example.secondary.domain.Product">
        UPDATE products
        <set>
            <if test="productCode != null">product_code = #{productCode,jdbcType=VARCHAR},</if>
            <if test="productName != null">product_name = #{productName,jdbcType=VARCHAR},</if>
            <if test="description != null">description = #{description,jdbcType=VARCHAR},</if>
            <if test="price != null">price = #{price,jdbcType=DECIMAL},</if>
            <if test="stock != null">stock = #{stock,jdbcType=INTEGER},</if>
            <if test="category != null">category = #{category,jdbcType=VARCHAR},</if>
            <if test="status != null">status = #{status,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除产品 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM products
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 批量插入产品 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO products (product_code, product_name, description, price, stock, category, status, create_time, update_time)
        VALUES
        <foreach collection="list" item="product" separator=",">
            (#{product.productCode,jdbcType=VARCHAR}, #{product.productName,jdbcType=VARCHAR}, #{product.description,jdbcType=VARCHAR},
             #{product.price,jdbcType=DECIMAL}, #{product.stock,jdbcType=INTEGER}, #{product.category,jdbcType=VARCHAR},
             #{product.status,jdbcType=VARCHAR}, #{product.createTime,jdbcType=TIMESTAMP}, #{product.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <!-- 批量删除产品 -->
    <delete id="batchDelete" parameterType="java.util.List">
        DELETE FROM products
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>

    <!-- 更新库存 -->
    <update id="updateStock">
        UPDATE products
        SET stock = #{stock,jdbcType=INTEGER},
            update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
