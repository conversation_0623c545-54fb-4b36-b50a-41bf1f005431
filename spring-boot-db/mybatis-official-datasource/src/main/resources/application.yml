# Spring Boot官方推荐多数据源配置
# 严格按照官方文档配置方式，使用DataSourceProperties + @ConfigurationProperties

# 主数据源配置 - 用户管理数据库
spring:
  datasource:
    url: *************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    # HikariCP连接池配置
    hikari:
      pool-name: PrimaryHikariPool
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
      # HikariCP高级配置
      leak-detection-threshold: 60000
      connection-init-sql: SELECT 1
      validation-timeout: 5000
      keepalive-time: 0
      allow-pool-suspension: false

# 第二数据源配置 - 订单管理数据库
app:
  datasource:
    url: ***************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    # HikariCP连接池特定配置
    configuration:
      pool-name: SecondaryHikariPool
      maximum-pool-size: 15
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
      # HikariCP高级配置
      leak-detection-threshold: 60000
      connection-init-sql: SELECT 1
      validation-timeout: 5000
      keepalive-time: 0
      allow-pool-suspension: false

# MyBatis配置
mybatis:
  # 映射文件位置
  mapper-locations: 
    - classpath:mapper/primary/*.xml
    - classpath:mapper/secondary/*.xml
  # 类型别名包
  type-aliases-package: com.example.primary.entity,com.example.secondary.entity
  # MyBatis全局配置
  configuration:
    # 下划线转驼峰命名
    map-underscore-to-camel-case: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 开启缓存
    cache-enabled: true
    # 使用生成的主键
    use-generated-keys: true
    # 延迟加载配置
    lazy-loading-enabled: true
    aggressive-lazy-loading: false

# 服务器配置
server:
  port: 8086

# 日志配置
logging:
  level:
    com.example: DEBUG
    com.zaxxer.hikari: INFO
    org.springframework.jdbc: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,datasource
  endpoint:
    health:
      show-details: always
