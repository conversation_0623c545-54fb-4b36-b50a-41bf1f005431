# 应用名称
spring.application.name=simple-mybatis-test

# H2数据库测试配置 - 优化版本
spring.datasource.url=jdbc:h2:mem:spring-boot-simple;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
spring.datasource.username=sa
spring.datasource.password=123456
spring.datasource.driver-class-name=org.h2.Driver

# H2数据库连接池配置
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.idle-timeout=300000

# H2控制台配置（用于调试）
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=true

# MyBatis配置
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.example.staff.domain
mybatis.type-handlers-package=com.example.staff.handler
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis.configuration.cache-enabled=false
mybatis.configuration.use-generated-keys=true

# 数据库初始化配置
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data.sql
spring.sql.init.continue-on-error=false

# 服务器配置
server.port=8081

# 测试环境特定配置
spring.profiles.active=test
