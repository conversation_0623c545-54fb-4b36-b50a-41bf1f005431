# EasyExcel 完整功能说明

## 功能概述

本模块实现了基于EasyExcel的完整Excel操作解决方案，包括Excel读取、写入（导出）和模板填充功能。

## 主要特性

### 📖 Excel 读取功能
1. **文件解析**：从simple.xlsx模板文件中读取学生数据
2. **数据监听**：使用ReadListener监听数据解析过程
3. **类型映射**：自动将Excel数据映射到StudentVo对象
4. **JSON输出**：读取结果以JSON格式返回

### 📝 Excel 写入功能
1. **数据导出**：将List<StudentVo>数据导出为Excel文件
2. **样式设置**：支持自定义表头和内容样式
3. **格式化**：专业的Excel格式，包括字体、颜色、对齐等
4. **文件下载**：直接通过HTTP响应流下载

### 📋 模板填充功能
1. **模板填充**：使用预定义的simple.xlsx模板文件
2. **数据填充**：支持List<StudentVo>数据填充
3. **格式保持**：保持模板原有格式和样式
4. **自定义数据**：支持通过API传入自定义学生数据

### 🔧 通用特性
1. **中文支持**：完美支持中文文件名，无乱码问题
2. **错误处理**：完善的异常处理和日志记录
3. **响应式设计**：测试页面支持移动端访问
4. **实时反馈**：操作结果实时显示

## API接口

### 📖 Excel 读取接口

#### 1. 读取Excel数据
**接口地址：** `GET /read`
**功能：** 从simple.xlsx模板文件中读取学生数据
**返回：** JSON格式的学生数据数组

**示例：**
```
GET http://localhost:8080/read
```

### 📝 Excel 写入接口

#### 1. 导出默认学生信息
**接口地址：** `GET /write/export`
**功能：** 导出预设的8条学生数据为格式化Excel文件
**文件名：** 学生信息导出.xlsx

**示例：**
```
GET http://localhost:8080/write/export
```

#### 2. 导出自定义学生信息
**接口地址：** `POST /write/exportCustom`
**功能：** 导出自定义学生数据为格式化Excel文件
**请求体：** JSON格式的学生数据数组
**文件名：** 自定义学生信息导出.xlsx

### 📋 模板填充接口

#### 1. 填充默认学生信息
**接口地址：** `GET /fill`
**功能：** 使用预设的8条学生数据填充模板并下载
**文件名：** 学生信息.xlsx

**示例：**
```
GET http://localhost:8080/fill
```

#### 2. 填充自定义数据
**接口地址：** `POST /fillWithCustomData`
**功能：** 使用自定义学生数据填充模板并下载
**请求体：** JSON格式的学生数据数组
**文件名：** 学生信息.xlsx

**示例：**
```json
[
  {
    "id": "1",
    "code": "2024001",
    "name": "张三",
    "age": "20",
    "province": "北京市",
    "city": "北京市"
  },
  {
    "id": "2",
    "code": "2024002",
    "name": "李四",
    "age": "21",
    "province": "上海市",
    "city": "上海市"
  }
]
```

## 测试页面

### 主测试页面
访问 `http://localhost:8080/index.html` 或 `http://localhost:8080/` 可以使用综合测试页面进行所有功能测试。

### 模板填充专用测试页面
访问 `http://localhost:8080/fill-test.html` 可以使用模板填充专用测试页面。

## 文件结构

```
src/main/
├── java/com/example/easyexcel/
│   ├── fill/controller/
│   │   └── FillController.java                # 模板填充控制器
│   ├── read/controller/
│   │   └── ReadController.java                # Excel读取控制器
│   ├── read/entity/
│   │   └── StudentVo.java                     # 学生实体类
│   ├── read/listener/
│   │   └── StudentVoReadListener.java         # Excel读取监听器
│   ├── write/controller/
│   │   └── WriteController.java               # Excel写入控制器
│   └── util/
│       └── TestFileUtil.java                  # 文件工具类
├── resources/
│   ├── templates/
│   │   └── simple.xlsx                        # Excel模板文件
│   └── static/
│       ├── index.html                         # 主测试页面
│       └── fill-test.html                     # 模板填充测试页面
```

## 核心代码说明

### FillController.java

主要包含以下方法：

1. **fill()** - 默认数据填充和下载
2. **fillWithCustomData()** - 自定义数据填充和下载
3. **createStudentList()** - 创建默认学生数据
4. **setDownloadResponseHeader()** - 设置下载响应头

### 关键技术点

1. **模板路径获取**：
   ```java
   String templateFileName = TestFileUtil.getPath() + "templates" + File.separator + "simple.xlsx";
   ```

2. **响应头设置**：
   ```java
   response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
   String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
   response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
   ```

3. **EasyExcel模板填充**：
   ```java
   EasyExcel.write(response.getOutputStream())
           .withTemplate(templateFileName)
           .sheet()
           .doFill(dataList);
   ```

## 使用步骤

1. **启动应用**：运行Spring Boot应用
2. **访问测试页面**：浏览器访问 `http://localhost:8080/fill-test.html`
3. **测试下载**：
   - 点击"下载默认学生信息"按钮测试默认数据下载
   - 修改JSON数据后点击"自定义数据下载"按钮测试自定义数据下载

## 注意事项

1. **模板文件**：确保simple.xlsx模板文件存在于`src/main/resources/templates/`目录下
2. **模板格式**：模板中使用`{}`来表示变量，如`{name}`、`{code}`等
3. **字段映射**：StudentVo实体类的字段需要与模板中的变量名对应
4. **中文编码**：已处理中文文件名编码问题，支持各种浏览器
5. **错误处理**：包含完善的异常处理，会在日志中记录详细错误信息

## 扩展功能

可以根据需要扩展以下功能：

1. **多模板支持**：支持选择不同的模板文件
2. **数据验证**：添加学生数据的验证逻辑
3. **批量处理**：支持大量数据的分批处理
4. **自定义文件名**：支持用户自定义下载文件名
5. **数据源集成**：从数据库或其他数据源获取学生数据

## 依赖说明

主要依赖：
- EasyExcel：Excel处理核心库
- Spring Boot Web：Web框架
- Lombok：简化代码编写

确保在pom.xml中包含相关依赖。
