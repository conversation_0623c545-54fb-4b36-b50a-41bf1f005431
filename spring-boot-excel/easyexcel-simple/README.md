# EasyExcel Simple 模块

## 项目简介

这是一个基于 Spring Boot 和 EasyExcel 的完整 Excel 操作示例项目，提供了 Excel 读取、写入和模板填充的完整解决方案。

## 功能特性

### 🚀 核心功能
- **Excel 读取**：从 Excel 文件中读取数据并转换为 Java 对象
- **Excel 写入**：将 Java 对象数据导出为格式化的 Excel 文件
- **模板填充**：使用预定义模板填充数据，保持原有格式

### 📊 数据模型
- **StudentVo**：学生信息实体类
  - `id`：学生ID
  - `code`：学生编码
  - `name`：学生姓名
  - `age`：学生年龄
  - `province`：所在省份
  - `city`：所在城市

### 🎨 界面特性
- **响应式设计**：支持桌面端和移动端访问
- **实时反馈**：操作结果实时显示
- **美观界面**：现代化的 UI 设计
- **中文支持**：完美支持中文文件名和内容

## 快速开始

### 1. 环境要求
- Java 17+
- Maven 3.6+
- Spring Boot 3.x

### 2. 启动应用
```bash
# 进入项目目录
cd spring-boot-excel/easyexcel-simple

# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 3. 访问测试页面
启动成功后，在浏览器中访问：
- **主测试页面**：http://localhost:8080/
- **模板填充测试**：http://localhost:8080/fill-test.html

## API 接口

### 📖 Excel 读取
```http
GET /read
```
从 simple.xlsx 模板文件中读取学生数据

### 📝 Excel 写入
```http
# 导出默认数据
GET /write/export

# 导出自定义数据
POST /write/exportCustom
Content-Type: application/json
[{"id":"1","code":"001","name":"张三",...}]
```

### 📋 模板填充
```http
# 填充默认数据
GET /fill

# 填充自定义数据
POST /fillWithCustomData
Content-Type: application/json
[{"id":"1","code":"001","name":"张三",...}]
```

## 项目结构

```
src/main/
├── java/com/example/easyexcel/
│   ├── EasyExcelSimpleApplication.java        # 启动类
│   ├── fill/controller/
│   │   └── FillController.java                # 模板填充控制器
│   ├── read/controller/
│   │   └── ReadController.java                # Excel读取控制器
│   ├── read/entity/
│   │   └── StudentVo.java                     # 学生实体类
│   ├── read/listener/
│   │   └── StudentVoReadListener.java         # Excel读取监听器
│   ├── write/controller/
│   │   └── WriteController.java               # Excel写入控制器
│   └── util/
│       └── TestFileUtil.java                  # 文件工具类
└── resources/
    ├── templates/
    │   └── simple.xlsx                        # Excel模板文件
    └── static/
        ├── index.html                         # 主测试页面
        └── fill-test.html                     # 模板填充测试页面
```

## 使用示例

### 1. Excel 读取
访问 `/read` 接口，系统会读取 `simple.xlsx` 文件中的数据并返回 JSON 格式结果。

### 2. Excel 写入
- **默认数据导出**：访问 `/write/export` 直接下载包含示例数据的 Excel 文件
- **自定义数据导出**：通过 POST 请求发送 JSON 数据到 `/write/exportCustom`

### 3. 模板填充
- **默认数据填充**：访问 `/fill` 使用模板填充示例数据
- **自定义数据填充**：通过 POST 请求发送 JSON 数据到 `/fillWithCustomData`

## 数据格式

### StudentVo JSON 格式
```json
[
  {
    "id": "1",
    "code": "2024001",
    "name": "张三",
    "age": "20",
    "province": "北京市",
    "city": "北京市"
  },
  {
    "id": "2",
    "code": "2024002",
    "name": "李四",
    "age": "21",
    "province": "上海市",
    "city": "上海市"
  }
]
```

## 技术栈

- **Spring Boot 3.x**：应用框架
- **EasyExcel**：Excel 操作核心库
- **Hutool**：Java 工具库
- **Lombok**：代码简化工具
- **Maven**：项目构建工具

## 注意事项

1. **模板文件**：确保 `simple.xlsx` 模板文件存在于 `src/main/resources/templates/` 目录
2. **字段映射**：StudentVo 实体类的字段需要与模板中的变量名对应
3. **中文编码**：已处理中文文件名编码问题，支持各种浏览器
4. **内存管理**：大数据量操作时注意内存使用情况

## 扩展功能

可以根据需要扩展以下功能：
- 多模板支持
- 数据验证
- 批量处理
- 数据库集成
- 文件上传解析

## 问题排查

### 常见问题
1. **模板文件不存在**：检查 `simple.xlsx` 文件是否在正确位置
2. **中文乱码**：确保系统编码设置为 UTF-8
3. **内存溢出**：处理大文件时调整 JVM 内存参数

### 日志查看
应用启动后会在控制台输出详细的操作日志，包括：
- 文件读取状态
- 数据处理进度
- 错误信息详情

## 许可证

本项目仅供学习和参考使用。
