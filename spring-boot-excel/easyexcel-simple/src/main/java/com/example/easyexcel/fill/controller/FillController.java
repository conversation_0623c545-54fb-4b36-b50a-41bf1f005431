package com.example.easyexcel.fill.controller;

import com.alibaba.excel.EasyExcel;
import com.example.easyexcel.read.entity.StudentVo;
import com.example.easyexcel.util.TestFileUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("all")
@Slf4j
@Controller
@RequestMapping
public class FillController {

    @Resource
    ResourceLoader resourceLoader;

    /**
     * 使用simple.xlsx模板填充List数据并下载Excel文件
     * 文件名设置为"学生信息"
     */
    @GetMapping("/fill")
    public void fill(HttpServletResponse response) throws IOException {
        try {

            // 模板注意 用{} 来表示你要用的变量 如果本来就有"{","}" 特殊字符 用"\{","\}"代替
            log.info("开始生成学生信息Excel文件");

            // 设置响应头，指定文件下载
            setDownloadResponseHeader(response, "学生信息.xlsx");

            // 获取模板文件路径
            String templateFileName = TestFileUtil.getPath() + "templates" + File.separator + "simple.xlsx";
            log.info("使用模板文件: {}", templateFileName);

            // 检查模板文件是否存在
            File templateFile = new File(templateFileName);
            if (!templateFile.exists()) {
                log.error("模板文件不存在: {}", templateFileName);
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "模板文件不存在");
                return;
            }

            // 准备填充数据
            List<StudentVo> dataList = createStudentList();
            log.info("准备填充 {} 条学生数据", dataList.size());

            // 使用EasyExcel填充模板并直接写入响应流
            EasyExcel.write(response.getOutputStream())
                    .withTemplate(templateFileName)
                    .sheet()
                    .doFill(dataList);

            log.info("学生信息Excel文件生成完成");

        } catch (Exception e) {
            log.error("生成Excel文件时发生错误", e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "生成Excel文件失败");
        }
    }

    /**
     * 使用自定义学生数据填充模板并下载Excel文件
     */
    @PostMapping("/fillWithCustomData")
    public void fillWithCustomData(@RequestBody List<StudentVo> studentList, HttpServletResponse response) throws IOException {
        try {
            log.info("开始生成自定义学生信息Excel文件");

            // 设置响应头，指定文件下载
            setDownloadResponseHeader(response, "学生信息.xlsx");

            // 获取模板文件路径
            String templateFileName = TestFileUtil.getPath() + "templates" + File.separator + "simple.xlsx";
            log.info("使用模板文件: {}", templateFileName);

            // 检查模板文件是否存在
            File templateFile = new File(templateFileName);
            if (!templateFile.exists()) {
                log.error("模板文件不存在: {}", templateFileName);
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "模板文件不存在");
                return;
            }

            // 检查数据是否为空
            if (studentList == null || studentList.isEmpty()) {
                log.warn("学生数据为空，使用默认数据");
                studentList = createStudentList();
            }

            log.info("准备填充 {} 条学生数据", studentList.size());

            // 使用EasyExcel填充模板并直接写入响应流
            EasyExcel.write(response.getOutputStream())
                    .withTemplate(templateFileName)
                    .sheet()
                    .doFill(studentList);

            log.info("自定义学生信息Excel文件生成完成");

        } catch (Exception e) {
            log.error("生成自定义Excel文件时发生错误", e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "生成Excel文件失败");
        }
    }

    /**
     * 创建学生数据列表
     */
    private List<StudentVo> createStudentList() {
        List<StudentVo> dataList = new ArrayList<>();

        // 创建示例学生数据
        StudentVo student1 = new StudentVo();
        student1.setId("1");
        student1.setCode("1001");
        student1.setName("张朝楠");
        student1.setAge("20");
        student1.setProvince("北京市");
        student1.setCity("北京市");

        StudentVo student2 = new StudentVo();
        student2.setId("2");
        student2.setCode("1002");
        student2.setName("李明");
        student2.setAge("21");
        student2.setProvince("上海市");
        student2.setCity("上海市");

        StudentVo student3 = new StudentVo();
        student3.setId("3");
        student3.setCode("1003");
        student3.setName("王小红");
        student3.setAge("19");
        student3.setProvince("广东省");
        student3.setCity("广州市");

        StudentVo student4 = new StudentVo();
        student4.setId("4");
        student4.setCode("1004");
        student4.setName("赵强");
        student4.setAge("22");
        student4.setProvince("江苏省");
        student4.setCity("南京市");

        StudentVo student5 = new StudentVo();
        student5.setId("5");
        student5.setCode("1005");
        student5.setName("刘美丽");
        student5.setAge("20");
        student5.setProvince("浙江省");
        student5.setCity("杭州市");

        StudentVo student6 = new StudentVo();
        student6.setId("6");
        student6.setCode("1006");
        student6.setName("陈伟");
        student6.setAge("23");
        student6.setProvince("四川省");
        student6.setCity("成都市");

        StudentVo student7 = new StudentVo();
        student7.setId("7");
        student7.setCode("1007");
        student7.setName("林小雨");
        student7.setAge("18");
        student7.setProvince("福建省");
        student7.setCity("福州市");

        StudentVo student8 = new StudentVo();
        student8.setId("8");
        student8.setCode("1008");
        student8.setName("黄志华");
        student8.setAge("24");
        student8.setProvince("湖南省");
        student8.setCity("长沙市");

        dataList.add(student1);
        dataList.add(student2);
        dataList.add(student3);
        dataList.add(student4);
        dataList.add(student5);
        dataList.add(student6);
        dataList.add(student7);
        dataList.add(student8);

        return dataList;
    }

    /**
     * 设置文件下载响应头
     */
    private void setDownloadResponseHeader(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        // 防止中文乱码
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
    }
}
