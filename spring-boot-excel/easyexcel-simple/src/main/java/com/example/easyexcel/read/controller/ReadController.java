package com.example.easyexcel.read.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.example.easyexcel.read.entity.StudentVo;
import com.example.easyexcel.read.listener.StudentVoReadListener;
import com.example.easyexcel.util.TestFileUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.io.IOException;
import java.util.List;

@Slf4j
@Controller
@RequestMapping
public class ReadController {

    @Resource
    ResourceLoader resourceLoader;

    @ResponseBody
    @RequestMapping(value = "/read")
    public List<StudentVo> read() throws IOException {
        StudentVoReadListener listener = new StudentVoReadListener();

        String fileName = TestFileUtil.getPath() + "templates" + File.separator + "simple.xlsx";
        EasyExcel.read(fileName, StudentVo.class, listener).sheet().doRead();


        // 从其他模块的 classpath 加载文件
        // InputStream inputStream = resourceLoader.getResource("classpath:templates/simple.xlsx").getInputStream();
        // EasyExcel.read(inputStream, Student.class, listener).sheet().doRead();

        List<StudentVo> dataList = listener.getDataList();
        log.info("获取数据:{}", JSONUtil.toJsonStr(dataList));
        return dataList;
    }
}
