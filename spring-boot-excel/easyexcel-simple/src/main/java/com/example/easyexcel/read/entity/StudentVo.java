package com.example.easyexcel.read.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class StudentVo implements Serializable {

    @ExcelProperty(value = "id", index = 0)
    private String id;

    @ExcelProperty(value = "编码", index = 1)
    private String code;

    @ExcelProperty(value = "姓名", index = 2)
    private String name;

    @ExcelProperty(value = "年龄", index = 3)
    private String age;

    @ExcelProperty(value = "省份", index = 4)
    private String province;

    @ExcelProperty(value = "城市", index = 5)
    private String city;
}
