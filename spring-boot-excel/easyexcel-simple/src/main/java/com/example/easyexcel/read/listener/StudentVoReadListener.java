package com.example.easyexcel.read.listener;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.example.easyexcel.read.entity.StudentVo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class StudentVoReadListener implements ReadListener<StudentVo> {

    private List<StudentVo> dataList = new ArrayList<>();

    @Override
    public void invoke(StudentVo studentVo, AnalysisContext analysisContext) {
        log.info("解析到一条数据:{}", JSONUtil.toJsonStr(studentVo));
        dataList.add(studentVo);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    public List<StudentVo> getDataList() {
        return dataList;
    }
}
