# 应用配置
spring.application.name=easyexcel-simple
server.port=8080

# 编码配置
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# 日志配置
logging.level.com.example.easyexcel=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# 静态资源配置
spring.web.resources.static-locations=classpath:/static/
spring.mvc.static-path-pattern=/**
