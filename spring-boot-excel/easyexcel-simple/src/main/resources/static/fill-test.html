<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel填充测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .button-group {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
        }
        button {
            padding: 12px 24px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .download-btn {
            background-color: #007bff;
            color: white;
        }
        .download-btn:hover {
            background-color: #0056b3;
        }
        .custom-btn {
            background-color: #28a745;
            color: white;
        }
        .custom-btn:hover {
            background-color: #1e7e34;
        }
        .form-section {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .form-section h3 {
            margin-top: 0;
            color: #555;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
        }
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info h4 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Excel模板填充测试</h1>
        
        <div class="info">
            <h4>功能说明：</h4>
            <p>1. <strong>下载默认学生信息</strong>：使用预设的5条学生数据填充simple.xlsx模板并下载</p>
            <p>2. <strong>自定义数据下载</strong>：使用下方JSON数据填充模板并下载</p>
            <p>3. 下载的文件名为：<strong>学生信息.xlsx</strong></p>
        </div>
        
        <div class="button-group">
            <button class="download-btn" onclick="downloadDefault()">下载默认学生信息</button>
            <button class="custom-btn" onclick="downloadCustom()">自定义数据下载</button>
        </div>
        
        <div class="form-section">
            <h3>自定义学生数据 (JSON格式)</h3>
            <textarea id="studentData" placeholder="请输入JSON格式的学生数据...">[
  {
    "id": "1",
    "code": "2024001",
    "name": "张三",
    "age": "20",
    "province": "北京市",
    "city": "北京市"
  },
  {
    "id": "2",
    "code": "2024002",
    "name": "李四",
    "age": "21",
    "province": "上海市",
    "city": "上海市"
  },
  {
    "id": "3",
    "code": "2024003",
    "name": "王五",
    "age": "19",
    "province": "广东省",
    "city": "深圳市"
  }
]</textarea>
        </div>
    </div>

    <script>
        // 下载默认学生信息
        function downloadDefault() {
            window.location.href = '/fill';
        }
        
        // 下载自定义数据
        function downloadCustom() {
            const studentData = document.getElementById('studentData').value;
            
            try {
                // 验证JSON格式
                const data = JSON.parse(studentData);
                
                // 创建表单并提交
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/fillWithCustomData';
                form.style.display = 'none';
                
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'studentData';
                input.value = JSON.stringify(data);
                
                form.appendChild(input);
                document.body.appendChild(form);
                
                // 使用fetch发送POST请求并处理文件下载
                fetch('/fillWithCustomData', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: studentData
                })
                .then(response => {
                    if (response.ok) {
                        return response.blob();
                    }
                    throw new Error('网络响应不正常');
                })
                .then(blob => {
                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = '学生信息.xlsx';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                })
                .catch(error => {
                    console.error('下载失败:', error);
                    alert('下载失败，请检查数据格式或稍后重试');
                });
                
                document.body.removeChild(form);
                
            } catch (e) {
                alert('JSON格式错误，请检查数据格式');
            }
        }
    </script>
</body>
</html>
