<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EasyExcel 功能测试中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        
        .feature-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #117a8b;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        .data-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }
        
        .data-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        textarea {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }
        
        textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }
        
        .info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .info-box h4 {
            color: #1976d2;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .info-box ul {
            color: #333;
            padding-left: 20px;
        }
        
        .info-box li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        
        .result-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            font-family: 'Consolas', 'Monaco', monospace;
            white-space: pre-wrap;
        }
        
        @media (max-width: 768px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 EasyExcel 功能测试中心</h1>
            <p>Excel 读取、写入、模板填充一站式测试平台</p>
        </div>
        
        <div class="content">
            <div class="info-box">
                <h4>📋 功能说明</h4>
                <ul>
                    <li><strong>Excel 读取</strong>：从 simple.xlsx 模板文件中读取学生数据并显示</li>
                    <li><strong>Excel 写入</strong>：将学生数据导出为格式化的 Excel 文件</li>
                    <li><strong>模板填充</strong>：使用预定义模板填充数据并下载</li>
                    <li><strong>自定义数据</strong>：支持使用自定义 JSON 数据进行操作</li>
                </ul>
            </div>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>📖 Excel 读取功能</h3>
                    <p>从 Excel 文件中读取学生数据，支持解析各种格式的学生信息，包括 ID、编码、姓名、年龄、省份、城市等字段。</p>
                    <div class="button-group">
                        <button class="btn btn-primary" onclick="readExcel()">读取 Excel 数据</button>
                        <button class="btn btn-info" onclick="clearResult()">清空结果</button>
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>📝 Excel 写入功能</h3>
                    <p>将学生数据导出为格式化的 Excel 文件，支持自定义样式、字体、颜色等，生成专业的数据报表。</p>
                    <div class="button-group">
                        <a href="/write/export" class="btn btn-success">导出默认数据</a>
                        <button class="btn btn-warning" onclick="exportCustomData()">导出自定义数据</button>
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>📋 模板填充功能</h3>
                    <p>使用预定义的 Excel 模板填充学生数据，保持原有格式和样式，适用于标准化报表生成。</p>
                    <div class="button-group">
                        <a href="/fill" class="btn btn-primary">填充默认数据</a>
                        <button class="btn btn-success" onclick="fillCustomData()">填充自定义数据</button>
                    </div>
                </div>
            </div>
            
            <div class="data-section">
                <h3>🔧 自定义学生数据 (JSON 格式)</h3>
                <textarea id="studentData" placeholder="请输入 JSON 格式的学生数据...">[
  {
    "id": "1",
    "code": "2024001",
    "name": "张三",
    "age": "20",
    "province": "北京市",
    "city": "北京市"
  },
  {
    "id": "2",
    "code": "2024002",
    "name": "李四",
    "age": "21",
    "province": "上海市",
    "city": "上海市"
  },
  {
    "id": "3",
    "code": "2024003",
    "name": "王五",
    "age": "19",
    "province": "广东省",
    "city": "深圳市"
  }
]</textarea>
            </div>
            
            <div class="result-area" id="resultArea">
                点击上方按钮开始测试功能...
            </div>
        </div>
    </div>

    <script>
        // 读取Excel数据
        function readExcel() {
            const resultArea = document.getElementById('resultArea');
            resultArea.textContent = '正在读取Excel数据...';
            
            fetch('/read')
                .then(response => response.json())
                .then(data => {
                    resultArea.textContent = '读取成功！数据如下：\n\n' + JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    console.error('读取失败:', error);
                    resultArea.textContent = '读取失败：' + error.message;
                });
        }
        
        // 导出自定义数据
        function exportCustomData() {
            const studentData = document.getElementById('studentData').value;
            
            try {
                const data = JSON.parse(studentData);
                
                fetch('/write/exportCustom', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: studentData
                })
                .then(response => {
                    if (response.ok) {
                        return response.blob();
                    }
                    throw new Error('网络响应不正常');
                })
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = '自定义学生信息导出.xlsx';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    document.getElementById('resultArea').textContent = '自定义数据导出成功！';
                })
                .catch(error => {
                    console.error('导出失败:', error);
                    document.getElementById('resultArea').textContent = '导出失败：' + error.message;
                });
                
            } catch (e) {
                alert('JSON格式错误，请检查数据格式');
            }
        }
        
        // 填充自定义数据
        function fillCustomData() {
            const studentData = document.getElementById('studentData').value;
            
            try {
                const data = JSON.parse(studentData);
                
                fetch('/fillWithCustomData', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: studentData
                })
                .then(response => {
                    if (response.ok) {
                        return response.blob();
                    }
                    throw new Error('网络响应不正常');
                })
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = '学生信息.xlsx';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    document.getElementById('resultArea').textContent = '自定义数据模板填充成功！';
                })
                .catch(error => {
                    console.error('填充失败:', error);
                    document.getElementById('resultArea').textContent = '填充失败：' + error.message;
                });
                
            } catch (e) {
                alert('JSON格式错误，请检查数据格式');
            }
        }
        
        // 清空结果
        function clearResult() {
            document.getElementById('resultArea').textContent = '结果已清空，点击上方按钮开始测试功能...';
        }
    </script>
</body>
</html>
