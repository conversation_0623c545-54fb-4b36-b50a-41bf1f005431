package com.example.easyexcel;

import com.example.easyexcel.read.entity.StudentVo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class EasyExcelSimpleApplicationTests {

    @Test
    void contextLoads() {
        // 测试应用上下文加载
        System.out.println("Spring Boot 应用上下文加载成功！");
    }

    @Test
    void testStudentVoCreation() {
        // 测试StudentVo对象创建
        StudentVo student = new StudentVo();
        student.setId("1");
        student.setCode("TEST001");
        student.setName("测试学生");
        student.setAge("20");
        student.setProvince("北京市");
        student.setCity("北京市");

        assert student.getId().equals("1");
        assert student.getCode().equals("TEST001");
        assert student.getName().equals("测试学生");
        assert student.getAge().equals("20");
        assert student.getProvince().equals("北京市");
        assert student.getCity().equals("北京市");

        System.out.println("StudentVo对象创建测试通过！");
    }

    @Test
    void testCreateStudentList() {
        // 测试创建学生列表
        List<StudentVo> studentList = createTestStudentList();

        assert studentList != null;
        assert studentList.size() == 2;
        assert studentList.get(0).getName().equals("测试学生1");
        assert studentList.get(1).getName().equals("测试学生2");

        System.out.println("学生列表创建测试通过！共创建了 " + studentList.size() + " 条数据");
    }

    /**
     * 创建测试用的学生数据
     */
    private List<StudentVo> createTestStudentList() {
        List<StudentVo> studentList = new ArrayList<>();

        StudentVo student1 = new StudentVo();
        student1.setId("1");
        student1.setCode("TEST001");
        student1.setName("测试学生1");
        student1.setAge("20");
        student1.setProvince("北京市");
        student1.setCity("北京市");

        StudentVo student2 = new StudentVo();
        student2.setId("2");
        student2.setCode("TEST002");
        student2.setName("测试学生2");
        student2.setAge("21");
        student2.setProvince("上海市");
        student2.setCity("上海市");

        studentList.add(student1);
        studentList.add(student2);

        return studentList;
    }
}
