package com.example.easyexcel;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Web集成测试类
 * 使用TestRestTemplate进行HTTP请求测试
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class WebIntegrationTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    void testReadExcelEndpoint() {
        // 测试Excel读取接口
        String url = "http://localhost:" + port + "/read";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().contains("["));
        
        System.out.println("Excel读取接口测试通过！");
        System.out.println("响应数据: " + response.getBody());
    }

    @Test
    void testExportExcelEndpoint() {
        // 测试Excel导出接口
        String url = "http://localhost:" + port + "/write/export";
        ResponseEntity<byte[]> response = restTemplate.getForEntity(url, byte[].class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().length > 0);
        
        // 检查响应头
        String contentDisposition = response.getHeaders().getFirst("Content-disposition");
        assertNotNull(contentDisposition);
        assertTrue(contentDisposition.contains("attachment"));
        
        System.out.println("Excel导出接口测试通过！");
        System.out.println("文件大小: " + response.getBody().length + " bytes");
    }

    @Test
    void testFillTemplateEndpoint() {
        // 测试模板填充接口
        String url = "http://localhost:" + port + "/fill";
        ResponseEntity<byte[]> response = restTemplate.getForEntity(url, byte[].class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().length > 0);
        
        // 检查响应头
        String contentDisposition = response.getHeaders().getFirst("Content-disposition");
        assertNotNull(contentDisposition);
        assertTrue(contentDisposition.contains("attachment"));
        
        System.out.println("模板填充接口测试通过！");
        System.out.println("文件大小: " + response.getBody().length + " bytes");
    }

    @Test
    void testStaticResourceAccess() {
        // 测试静态资源访问
        String indexUrl = "http://localhost:" + port + "/index.html";
        ResponseEntity<String> indexResponse = restTemplate.getForEntity(indexUrl, String.class);
        
        assertEquals(HttpStatus.OK, indexResponse.getStatusCode());
        assertNotNull(indexResponse.getBody());
        assertTrue(indexResponse.getBody().contains("EasyExcel"));
        
        String fillTestUrl = "http://localhost:" + port + "/fill-test.html";
        ResponseEntity<String> fillTestResponse = restTemplate.getForEntity(fillTestUrl, String.class);
        
        assertEquals(HttpStatus.OK, fillTestResponse.getStatusCode());
        assertNotNull(fillTestResponse.getBody());
        assertTrue(fillTestResponse.getBody().contains("Excel"));
        
        System.out.println("静态资源访问测试通过！");
    }

    @Test
    void testApplicationHealth() {
        // 测试应用健康状态
        String url = "http://localhost:" + port + "/read";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        // 如果能正常响应，说明应用启动正常
        assertTrue(response.getStatusCode().is2xxSuccessful() || 
                  response.getStatusCode().is4xxClientError());
        
        System.out.println("应用健康状态测试通过！");
        System.out.println("应用运行在端口: " + port);
    }
}
