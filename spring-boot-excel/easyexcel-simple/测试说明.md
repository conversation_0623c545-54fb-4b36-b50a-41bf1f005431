# EasyExcel Simple 测试说明

## 测试环境

### 依赖问题解决

如果遇到 `@AutoConfigureTestMvc` 找不到的问题，这是正常的，因为我们已经简化了测试类，不再使用这个注解。

### 当前测试结构

```
src/test/java/com/example/easyexcel/
├── EasyExcelSimpleApplicationTests.java    # 基础单元测试
└── WebIntegrationTest.java                 # Web集成测试（可选）
```

## 测试类说明

### 1. EasyExcelSimpleApplicationTests.java

**基础单元测试类**，包含以下测试：

- `contextLoads()` - 测试Spring Boot应用上下文加载
- `testStudentVoCreation()` - 测试StudentVo对象创建和属性设置
- `testCreateStudentList()` - 测试学生数据列表创建

**运行方式：**
```bash
mvn test -Dtest=EasyExcelSimpleApplicationTests
```

### 2. WebIntegrationTest.java

**Web集成测试类**，包含以下测试：

- `testReadExcelEndpoint()` - 测试Excel读取接口
- `testExportExcelEndpoint()` - 测试Excel导出接口
- `testFillTemplateEndpoint()` - 测试模板填充接口
- `testStaticResourceAccess()` - 测试静态资源访问
- `testApplicationHealth()` - 测试应用健康状态

**运行方式：**
```bash
mvn test -Dtest=WebIntegrationTest
```

## 运行所有测试

### 1. 命令行运行

```bash
# 进入项目目录
cd spring-boot-excel/easyexcel-simple

# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=EasyExcelSimpleApplicationTests

# 运行特定测试方法
mvn test -Dtest=EasyExcelSimpleApplicationTests#contextLoads
```

### 2. IDE运行

在IDE中右键点击测试类或测试方法，选择"Run Test"即可。

## 测试前准备

### 1. 确保模板文件存在

确保以下文件存在：
```
src/main/resources/templates/simple.xlsx
```

### 2. 确保依赖正确

检查pom.xml中是否包含以下依赖：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

### 3. 编译项目

```bash
mvn clean compile
```

## 测试结果说明

### 成功输出示例

```
Spring Boot 应用上下文加载成功！
StudentVo对象创建测试通过！
学生列表创建测试通过！共创建了 2 条数据
Excel读取接口测试通过！
Excel导出接口测试通过！
模板填充接口测试通过！
静态资源访问测试通过！
应用健康状态测试通过！
```

### 常见问题

1. **模板文件不存在**
   ```
   错误：模板文件不存在: /path/to/simple.xlsx
   解决：确保simple.xlsx文件在正确位置
   ```

2. **端口占用**
   ```
   错误：Port 8080 was already in use
   解决：关闭占用8080端口的程序，或修改application.properties中的端口
   ```

3. **编码问题**
   ```
   错误：中文乱码
   解决：确保IDE和系统编码设置为UTF-8
   ```

## 手动测试

如果自动化测试有问题，可以进行手动测试：

### 1. 启动应用

```bash
mvn spring-boot:run
```

### 2. 访问测试页面

- 主页面：http://localhost:8080/index.html
- 填充测试：http://localhost:8080/fill-test.html

### 3. 测试API接口

```bash
# 测试读取接口
curl http://localhost:8080/read

# 测试导出接口（会下载文件）
curl -O -J http://localhost:8080/write/export

# 测试填充接口（会下载文件）
curl -O -J http://localhost:8080/fill
```

## 性能测试

### 大数据量测试

可以修改控制器中的`createStudentList()`方法，增加更多数据来测试性能：

```java
// 创建1000条数据进行性能测试
for (int i = 1; i <= 1000; i++) {
    StudentVo student = new StudentVo();
    student.setId(String.valueOf(i));
    student.setCode("CODE" + String.format("%04d", i));
    student.setName("学生" + i);
    // ... 设置其他属性
    dataList.add(student);
}
```

### 内存监控

在测试大数据量时，可以添加JVM参数监控内存使用：

```bash
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xms512m -Xmx1024m -XX:+PrintGCDetails"
```

## 总结

- 基础测试不需要复杂的Web测试框架
- 使用TestRestTemplate进行集成测试
- 确保模板文件和依赖正确配置
- 可以通过手动测试验证功能
