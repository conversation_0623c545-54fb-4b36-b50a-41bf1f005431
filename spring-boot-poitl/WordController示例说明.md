# WordController - POI-TL 官网示例集合

## 项目简介

本项目基于 POI-TL 官网（https://deepoove.com/poi-tl/）的文档和示例，创建了一个完整的 WordController，展示了 POI-TL 模板引擎的各种功能和使用场景。

## 功能特性

### 🎯 核心功能
- **文本处理**: 支持普通文本、样式文本、超链接、锚点
- **图片插入**: 支持本地图片、网络图片、指定尺寸
- **表格生成**: 支持基础表格、样式表格、合并单元格
- **列表创建**: 支持有序列表、无序列表、多种编号格式
- **条件渲染**: 支持条件显示和循环渲染
- **模板复用**: 模板即样式，所见即所得

### 🚀 高级特性
- **区块对功能**: 条件显示、循环渲染、内置变量
- **复杂表格**: 单元格合并、样式设置、动态数据
- **业务场景**: 合同、报表、API文档等实际应用
- **样式控制**: 字体、颜色、大小、对齐等完整样式支持

## API接口列表

### 1. 基础功能示例

| 接口路径 | 功能描述 | 主要特性 |
|---------|---------|---------|
| `/word/basic-text` | 基础文本示例 | 文本标签、样式设置、超链接、锚点 |
| `/word/image-example` | 图片示例 | 本地图片、网络图片、尺寸控制 |
| `/word/table-example` | 表格示例 | 基础表格、样式表格、单元格合并 |
| `/word/list-example` | 列表示例 | 有序列表、无序列表、编号格式 |
| `/word/section-example` | 区块对示例 | 条件显示、循环渲染、内置变量 |

### 2. 综合应用示例

| 接口路径 | 功能描述 | 应用场景 |
|---------|---------|---------|
| `/word/employee-report` | 员工报告 | 人事管理、绩效评估 |
| `/word/contract-example` | 合同模板 | 商务合同、协议文档 |
| `/word/data-report` | 数据报表 | 销售报表、数据分析 |
| `/word/api-doc` | API文档 | 技术文档、接口规范 |

### 3. 工具接口

| 接口路径 | 功能描述 | 说明 |
|---------|---------|-----|
| `/word/examples` | 示例索引 | 返回所有可用示例的详细说明 |
| `/word/generate-word` | 原有示例 | 保持兼容性的用户信息表格 |

## 技术实现

### 核心依赖
```xml
<dependency>
    <groupId>com.deepoove</groupId>
    <artifactId>poi-tl</artifactId>
    <version>1.12.2</version>
</dependency>
```

### 主要类和方法

#### 1. 文本处理
```java
// 普通文本
data.put("name", "张三");

// 样式文本
data.put("title", Texts.of("标题").bold().fontSize(16).color("FF0000").create());

// 超链接
data.put("website", Texts.of("官网").link("https://example.com").create());

// 锚点
data.put("anchor", Texts.of("详情").anchor("detail_section").create());
```

#### 2. 图片处理
```java
// 本地图片
data.put("logo", Pictures.ofLocal("logo.png").size(120, 80).create());

// 网络图片
data.put("badge", Pictures.ofUrl("https://example.com/badge.svg").size(100, 20).create());
```

#### 3. 表格处理
```java
// 基础表格
TableRenderData table = Tables.of(new String[][] {
    new String[] { "姓名", "年龄" },
    new String[] { "张三", "28" }
}).border(BorderStyle.DEFAULT).create();

// 样式表格
RowRenderData header = Rows.of("姓名", "年龄")
    .bgColor("4472C4").textColor("FFFFFF").center().create();
```

#### 4. 列表处理
```java
// 有序列表
data.put("list", Numberings.of(NumberingFormat.DECIMAL)
    .addItem("第一项")
    .addItem("第二项")
    .create());
```

#### 5. 区块对处理
```java
// 条件数据
data.put("show_details", true);

// 循环数据
List<Map<String, Object>> items = Arrays.asList(
    Map.of("name", "张三", "age", 28),
    Map.of("name", "李四", "age", 30)
);
data.put("employees", items);
```

## 模板文件说明

### 标签语法
- **文本标签**: `{{变量名}}`
- **图片标签**: `{{@变量名}}`
- **表格标签**: `{{#变量名}}`
- **列表标签**: `{{*变量名}}`
- **区块对**: `{{?变量名}}...{{/变量名}}`

### 内置变量（区块对中可用）
- `{{_index}}` - 循环索引（从0开始）
- `{{_is_first}}` - 是否为第一项
- `{{_is_last}}` - 是否为最后一项
- `{{_has_next}}` - 是否有下一项
- `{{_is_even_item}}` - 是否为偶数项
- `{{_is_odd_item}}` - 是否为奇数项

## 快速开始

### 1. 启动项目
```bash
mvn spring-boot:run
```

### 2. 访问示例索引
```
http://localhost:8080/word/examples
```

### 3. 测试基础功能
```
http://localhost:8080/word/basic-text?name=张三&position=工程师
```

### 4. 下载生成的文档
所有接口都会返回Word文档文件，浏览器会自动下载。

## 模板文件创建

### 1. 创建模板目录
```
src/main/resources/templates/
```

### 2. 使用Word软件创建模板
- 使用Microsoft Word、WPS Office等创建.docx文件
- 在需要动态内容的位置插入标签
- 设置标签的样式（字体、颜色、大小等）
- 保存到templates目录

### 3. 模板示例
```
{{title}}

姓名：{{name}}
职位：{{position}}

{{@photo}}

工作经历：
{{?experiences}}
{{_index}}. {{company}} - {{position}} ({{duration}})
{{/experiences}}
```

## 实际应用场景

### 1. 企业文档自动化
- 员工报告生成
- 合同文档批量生成
- 证书奖状制作

### 2. 数据报表
- 销售报表
- 财务报表
- 统计分析报告

### 3. 技术文档
- API接口文档
- 系统说明书
- 用户手册

### 4. 业务流程
- 审批文档
- 通知公告
- 会议纪要

## 扩展功能

### 1. 插件支持
POI-TL支持插件机制，可以扩展更多功能：
- 代码高亮插件
- Markdown插件
- 图表插件
- 批注插件

### 2. 高级特性
- Spring表达式支持
- 模板嵌套
- 文档合并
- 自定义函数

## 注意事项

### 1. 模板设计
- 模板即样式，在模板中设置好样式
- 使用表格布局可以创建更专业的文档
- 注意标签的命名规范

### 2. 性能优化
- 大量数据时考虑分页处理
- 图片尺寸要合理控制
- 模板文件不要过于复杂

### 3. 错误处理
- 检查模板文件是否存在
- 验证数据格式是否正确
- 处理文件生成异常

## 参考资源

- [POI-TL官网](https://deepoove.com/poi-tl/)
- [Apache POI文档](https://poi.apache.org/)
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [模板文件创建说明](./模板文件创建说明.md)

## 版本信息

- POI-TL版本：1.12.2
- Spring Boot版本：3.x
- JDK版本：17+
- 项目版本：2.0

---

**注意**: 本示例基于POI-TL官网文档创建，展示了该模板引擎的核心功能和最佳实践。在实际项目中使用时，请根据具体需求进行调整和优化。
