package com.example.controller;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;
import jakarta.annotation.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 业务场景示例控制器
 * 演示实际业务中的文档生成场景，直观展示POI-TL的实际应用
 * 基于Apache POI官方文档：https://poi.apache.org/
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/business")
public class BusinessExampleController {

    @Resource
    private ResourceLoader resourceLoader;

    /**
     * 生成员工入职合同
     * 实际业务场景：HR部门生成新员工入职合同
     */
    @GetMapping("/employment-contract")
    public ResponseEntity<byte[]> generateEmploymentContract(
            @RequestParam(defaultValue = "张三") String employeeName,
            @RequestParam(defaultValue = "Java开发工程师") String position,
            @RequestParam(defaultValue = "技术部") String department,
            @RequestParam(defaultValue = "8000") String salary) throws IOException {
        
        Map<String, Object> data = new HashMap<>();
        
        // 1. 基本信息
        data.put("companyName", "XX科技有限公司");
        data.put("employeeName", employeeName);
        data.put("position", position);
        data.put("department", department);
        data.put("salary", salary);
        data.put("contractDate", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
        data.put("contractNo", "HT" + System.currentTimeMillis());
        
        // 2. 合同期限
        data.put("startDate", "2024年1月1日");
        data.put("endDate", "2026年12月31日");
        data.put("probationPeriod", "3个月");
        
        // 3. 工作内容描述
        data.put("jobDescription", "负责Java后端开发工作，参与系统设计和代码实现，配合团队完成项目开发任务。");
        
        // 4. 薪资福利表格
        RowRenderData salaryHeader = Rows.of("项目", "标准", "备注")
                .textColor("FFFFFF")
                .bgColor("4472C4")
                .center()
                .bold()
                .create();
        
        RowRenderData basicSalary = Rows.of("基本工资", salary + "元/月", "按月发放").center().create();
        RowRenderData bonus = Rows.of("绩效奖金", "根据考核确定", "季度发放").center().create();
        RowRenderData insurance = Rows.of("五险一金", "按国家标准", "公司承担").center().create();
        RowRenderData annual = Rows.of("年假", "5-15天", "根据工龄").center().create();
        
        TableRenderData salaryTable = Tables.of(salaryHeader, basicSalary, bonus, insurance, annual)
                .border(BorderStyle.DEFAULT)
                .create();
        data.put("salaryTable", salaryTable);
        
        // 5. 重要条款列表
        NumberingRenderData importantClauses = Numberings.ofDecimal(
                "员工应遵守公司各项规章制度",
                "严格保守公司商业秘密和技术秘密",
                "不得从事与公司业务相竞争的活动",
                "合同期内不得随意离职，如需离职应提前30天书面通知"
        ).create();
        data.put("importantClauses", importantClauses);
        
        // 6. 签名区域
        data.put("signatureDate", "签署日期：_______年____月____日");
        
        return generateDocument("classpath:templates/employment_contract_template.docx", 
                                data, "employment_contract_" + employeeName + ".docx");
    }

    /**
     * 生成月度销售报告
     * 实际业务场景：销售部门月度业绩汇报
     */
    @GetMapping("/sales-report")
    public ResponseEntity<byte[]> generateSalesReport(
            @RequestParam(defaultValue = "2024年1月") String reportMonth) throws IOException {
        
        Map<String, Object> data = new HashMap<>();
        
        // 1. 报告基本信息
        data.put("reportMonth", reportMonth);
        data.put("reportDate", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
        data.put("reporter", "销售部经理");
        data.put("department", "销售部");
        
        // 2. 业绩摘要标题
        TextRenderData summaryTitle = Texts.of("业绩摘要")
                .color("2F5597")
                .bold()
                .fontSize(16)
                .create();
        data.put("summaryTitle", summaryTitle);
        
        // 3. 关键指标
        data.put("totalSales", "150万元");
        data.put("growthRate", "15%");
        data.put("targetCompletion", "120%");
        
        // 4. 销售数据表格
        RowRenderData salesHeader = Rows.of("产品类别", "销售额(万元)", "同比增长", "市场占比", "完成率")
                .textColor("FFFFFF")
                .bgColor("2F5597")
                .center()
                .bold()
                .create();
        
        RowRenderData product1 = Rows.of("智能硬件", "80", "+25%", "53.3%", "130%").center().create();
        RowRenderData product2 = Rows.of("软件服务", "45", "+10%", "30.0%", "110%").center().create();
        RowRenderData product3 = Rows.of("技术咨询", "25", "+5%", "16.7%", "100%").center().create();
        
        TableRenderData salesTable = Tables.of(salesHeader, product1, product2, product3)
                .border(BorderStyle.DEFAULT)
                .create();
        data.put("salesTable", salesTable);
        
        // 5. 重点客户列表
        NumberingRenderData keyCustomers = Numberings.ofDecimal(
                "ABC科技有限公司 - 签约金额：35万元 - 智能硬件采购",
                "XYZ制造集团 - 签约金额：28万元 - 软件定制开发",
                "DEF贸易公司 - 签约金额：22万元 - 系统集成服务",
                "GHI创新企业 - 签约金额：18万元 - 技术咨询服务"
        ).create();
        data.put("keyCustomers", keyCustomers);
        
        // 6. 下月计划
        NumberingRenderData nextPlan = Numberings.create(
                "加强新产品推广，重点推进智能硬件2.0版本",
                "深化与重点客户合作，提升客户满意度",
                "开拓新市场渠道，扩大品牌影响力",
                "加强团队培训，提升销售技能"
        );
        data.put("nextPlan", nextPlan);
        
        // 7. 风险提示
        TextRenderData riskWarning = Texts.of("风险提示：市场竞争加剧，需要加强产品差异化和服务质量。")
                .color("FF6600")
                .bold()
                .create();
        data.put("riskWarning", riskWarning);
        
        return generateDocument("classpath:templates/sales_report_template.docx", 
                                data, "sales_report_" + reportMonth.replace("年", "").replace("月", "") + ".docx");
    }

    /**
     * 生成项目验收报告
     * 实际业务场景：项目完成后的验收文档
     */
    @GetMapping("/project-acceptance")
    public ResponseEntity<byte[]> generateProjectAcceptance(
            @RequestParam(defaultValue = "客户管理系统") String projectName,
            @RequestParam(defaultValue = "ABC公司") String clientName) throws IOException {
        
        Map<String, Object> data = new HashMap<>();
        
        // 1. 项目基本信息
        data.put("projectName", projectName);
        data.put("clientName", clientName);
        data.put("projectManager", "李项目经理");
        data.put("acceptanceDate", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
        data.put("projectCode", "PRJ" + System.currentTimeMillis());
        
        // 2. 项目概述
        data.put("projectOverview", "本项目为" + clientName + "定制开发的" + projectName + "，" +
                "采用Spring Boot + Vue.js技术栈，实现了用户管理、权限控制、数据统计等核心功能。");
        
        // 3. 功能验收表格
        RowRenderData functionHeader = Rows.of("功能模块", "验收标准", "测试结果", "验收状态")
                .textColor("FFFFFF")
                .bgColor("4472C4")
                .center()
                .bold()
                .create();
        
        RowRenderData func1 = Rows.of("用户管理", "增删改查功能正常", "通过", "✓").center().create();
        RowRenderData func2 = Rows.of("权限控制", "角色权限分配正确", "通过", "✓").center().create();
        RowRenderData func3 = Rows.of("数据统计", "报表生成准确", "通过", "✓").center().create();
        RowRenderData func4 = Rows.of("系统性能", "响应时间<2秒", "通过", "✓").center().create();
        
        TableRenderData functionTable = Tables.of(functionHeader, func1, func2, func3, func4)
                .border(BorderStyle.DEFAULT)
                .create();
        data.put("functionTable", functionTable);
        
        // 4. 技术指标表格
        RowRenderData techHeader = Rows.of("技术指标", "要求", "实际值", "是否达标")
                .textColor("FFFFFF")
                .bgColor("6C9BD1")
                .center()
                .bold()
                .create();
        
        RowRenderData tech1 = Rows.of("并发用户数", "≥100", "150", "达标").center().create();
        RowRenderData tech2 = Rows.of("响应时间", "≤2秒", "1.2秒", "达标").center().create();
        RowRenderData tech3 = Rows.of("数据准确率", "≥99.9%", "99.95%", "达标").center().create();
        RowRenderData tech4 = Rows.of("系统可用性", "≥99%", "99.8%", "达标").center().create();
        
        TableRenderData techTable = Tables.of(techHeader, tech1, tech2, tech3, tech4)
                .border(BorderStyle.DEFAULT)
                .create();
        data.put("techTable", techTable);
        
        // 5. 验收结论
        TextRenderData conclusion = Texts.of("验收结论：项目各项功能和技术指标均达到合同要求，验收通过。")
                .color("00AA00")
                .bold()
                .fontSize(14)
                .create();
        data.put("conclusion", conclusion);
        
        // 6. 后续服务
        NumberingRenderData afterService = Numberings.create(
                "提供3个月免费技术支持",
                "提供系统操作培训",
                "提供系统维护文档",
                "建立长期技术支持渠道"
        );
        data.put("afterService", afterService);
        
        return generateDocument("classpath:templates/project_acceptance_template.docx", 
                                data, "project_acceptance_" + projectName + ".docx");
    }

    /**
     * 通用文档生成方法
     * 避免重复代码，统一处理文档生成逻辑
     */
    private ResponseEntity<byte[]> generateDocument(String templatePath, Map<String, Object> data, String filename) throws IOException {
        InputStream templateStream = resourceLoader.getResource(templatePath).getInputStream();
        XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        template.write(outputStream);
        template.close();
        templateStream.close();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", filename);
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());
    }

    /**
     * 获取业务示例说明
     */
    @GetMapping("/examples-info")
    public ResponseEntity<Map<String, Object>> getBusinessExamplesInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("title", "POI-TL业务场景示例");
        info.put("description", "实际业务中的文档生成应用场景");
        
        Map<String, Object> examples = new HashMap<>();
        examples.put("employment-contract", "员工入职合同 - 参数：employeeName, position, department, salary");
        examples.put("sales-report", "月度销售报告 - 参数：reportMonth");
        examples.put("project-acceptance", "项目验收报告 - 参数：projectName, clientName");
        
        info.put("examples", examples);
        info.put("baseUrl", "/business");
        info.put("note", "所有示例都支持参数自定义，直接在浏览器访问即可下载文档");
        
        return ResponseEntity.ok(info);
    }
}
