package com.example.controller;

import com.example.service.DocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

/**
 * POI示例控制器
 * 提供各种POI文档生成的API接口
 * 基于Apache POI官方文档的最佳实践
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/api/poi-examples")
public class PoiExampleController {

    @Autowired
    private DocumentService documentService;

    /**
     * 生成基础文本文档示例
     * 演示基本的文本替换和格式设置
     * 
     * @param data 文档数据
     * @return Word文档字节流
     */
    @PostMapping("/basic-text")
    public ResponseEntity<byte[]> generateBasicText(@RequestBody(required = false) Map<String, Object> data) {
        try {
            if (data == null) {
                data = new HashMap<>();
            }
            
            byte[] document = documentService.generateBasicTextDocument(data);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "basic_text_example.docx");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(document);
                    
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 生成表格文档示例
     * 演示各种表格操作：简单表格、合并单元格、动态表格
     * 
     * @param tableData 表格数据列表
     * @return Word文档字节流
     */
    @PostMapping("/table")
    public ResponseEntity<byte[]> generateTable(@RequestBody(required = false) List<Map<String, Object>> tableData) {
        try {
            if (tableData == null) {
                // 提供默认示例数据
                tableData = Arrays.asList(
                    Map.of("姓名", "张三", "年龄", 28, "部门", "技术部", "薪资", 8000),
                    Map.of("姓名", "李四", "年龄", 30, "部门", "市场部", "薪资", 7500),
                    Map.of("姓名", "王五", "年龄", 25, "部门", "人事部", "薪资", 6500)
                );
            }
            
            byte[] document = documentService.generateTableDocument(tableData);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "table_example.docx");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(document);
                    
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 生成图片文档示例
     * 演示图片插入、大小调整等功能
     * 
     * @param imageData 图片相关数据
     * @return Word文档字节流
     */
    @PostMapping("/image")
    public ResponseEntity<byte[]> generateImage(@RequestBody(required = false) Map<String, Object> imageData) {
        try {
            if (imageData == null) {
                imageData = new HashMap<>();
                // 可以添加默认图片路径
                // imageData.put("imagePath", "classpath:static/images/sample.jpg");
            }
            
            byte[] document = documentService.generateImageDocument(imageData);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "image_example.docx");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(document);
                    
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 生成列表文档示例
     * 演示有序列表和无序列表
     * 
     * @param listData 列表数据
     * @return Word文档字节流
     */
    @PostMapping("/list")
    public ResponseEntity<byte[]> generateList(@RequestBody(required = false) Map<String, Object> listData) {
        try {
            if (listData == null) {
                listData = new HashMap<>();
                listData.put("items", Arrays.asList(
                    "POI-TL基础功能介绍",
                    "文本处理和格式设置",
                    "表格创建和数据填充",
                    "图片插入和调整",
                    "列表和编号处理",
                    "高级功能应用"
                ));
            }
            
            byte[] document = documentService.generateListDocument(listData);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "list_example.docx");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(document);
                    
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 生成高级功能文档示例
     * 演示书签、超链接、代码高亮等高级功能
     * 
     * @param advancedData 高级功能数据
     * @return Word文档字节流
     */
    @PostMapping("/advanced")
    public ResponseEntity<byte[]> generateAdvanced(@RequestBody(required = false) Map<String, Object> advancedData) {
        try {
            if (advancedData == null) {
                advancedData = new HashMap<>();
            }
            
            byte[] document = documentService.generateAdvancedDocument(advancedData);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "advanced_example.docx");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(document);
                    
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取所有可用的示例列表
     * 
     * @return 示例列表和说明
     */
    @GetMapping("/examples")
    public ResponseEntity<Map<String, Object>> getExamples() {
        Map<String, Object> examples = new HashMap<>();
        
        examples.put("basic-text", Map.of(
            "name", "基础文本示例",
            "description", "演示基本的文本替换、格式设置和样式应用",
            "method", "POST",
            "endpoint", "/api/poi-examples/basic-text",
            "parameters", Map.of(
                "author", "作者姓名（可选）",
                "content", "文档内容（可选）"
            )
        ));
        
        examples.put("table", Map.of(
            "name", "表格示例",
            "description", "演示表格创建、合并单元格、样式设置和动态数据填充",
            "method", "POST",
            "endpoint", "/api/poi-examples/table",
            "parameters", "表格数据数组，每个元素为键值对对象"
        ));
        
        examples.put("image", Map.of(
            "name", "图片示例",
            "description", "演示图片插入、大小调整和位置设置",
            "method", "POST",
            "endpoint", "/api/poi-examples/image",
            "parameters", Map.of(
                "imagePath", "本地图片路径（可选）",
                "imageUrl", "网络图片URL（可选）"
            )
        ));
        
        examples.put("list", Map.of(
            "name", "列表示例",
            "description", "演示有序列表和无序列表的创建",
            "method", "POST",
            "endpoint", "/api/poi-examples/list",
            "parameters", Map.of(
                "items", "列表项数组"
            )
        ));
        
        examples.put("advanced", Map.of(
            "name", "高级功能示例",
            "description", "演示书签、超链接、代码高亮等高级功能",
            "method", "POST",
            "endpoint", "/api/poi-examples/advanced",
            "parameters", "高级功能相关参数"
        ));
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "POI示例列表");
        response.put("examples", examples);
        response.put("documentation", "基于Apache POI官方文档和poi-tl最佳实践");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("service", "POI Examples Service");
        status.put("version", "1.0.0");
        status.put("timestamp", new Date());
        
        return ResponseEntity.ok(status);
    }
}
