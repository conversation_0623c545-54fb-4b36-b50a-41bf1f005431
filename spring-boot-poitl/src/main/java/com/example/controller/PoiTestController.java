package com.example.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;
import java.util.List;

/**
 * POI测试控制器
 * 提供所有POI示例的统一入口和说明
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/poi-test")
public class PoiTestController {

    /**
     * 获取所有POI示例的完整说明
     */
    @GetMapping("/all-examples")
    public ResponseEntity<Map<String, Object>> getAllExamples() {
        Map<String, Object> response = new HashMap<>();
        
        response.put("title", "POI-TL完整示例集合");
        response.put("description", "基于Apache POI官方文档的poi-tl实用示例");
        response.put("officialDocs", "https://deepoove.com/poi-tl/");
        response.put("apachePoi", "https://poi.apache.org/");
        
        // 基础功能示例
        Map<String, Object> basicExamples = new HashMap<>();
        basicExamples.put("basic-text", Map.of(
            "url", "/basic-text",
            "description", "基础文本处理 - 文本替换、样式设置",
            "features", Arrays.asList("文本颜色", "字体大小", "粗体斜体", "组合样式")
        ));
        
        basicExamples.put("simple-table", Map.of(
            "url", "/simple-table", 
            "description", "简单表格 - 基础表格创建和样式",
            "features", Arrays.asList("表头样式", "数据行格式", "居中对齐", "边框设置")
        ));
        
        basicExamples.put("complex-table", Map.of(
            "url", "/complex-table",
            "description", "复杂表格 - 合并单元格和多层表头",
            "features", Arrays.asList("单元格合并", "多层表头", "复杂样式", "分组显示")
        ));
        
        basicExamples.put("list-example", Map.of(
            "url", "/list-example",
            "description", "列表功能 - 有序无序列表",
            "features", Arrays.asList("无序列表", "数字编号", "字母编号", "自定义样式")
        ));
        
        basicExamples.put("image-example", Map.of(
            "url", "/image-example",
            "description", "图片处理 - 图片插入和调整",
            "features", Arrays.asList("本地图片", "网络图片", "Base64图片", "尺寸调整")
        ));
        
        basicExamples.put("hyperlink-example", Map.of(
            "url", "/hyperlink-example",
            "description", "超链接 - 网页和邮箱链接",
            "features", Arrays.asList("网页链接", "邮箱链接", "链接样式", "颜色设置")
        ));
        
        basicExamples.put("comprehensive-report", Map.of(
            "url", "/comprehensive-report",
            "description", "综合报告 - 多元素组合文档",
            "features", Arrays.asList("标题样式", "表格数据", "列表内容", "重点提醒")
        ));
        
        basicExamples.put("dynamic-table", Map.of(
            "url", "/dynamic-table",
            "description", "动态表格 - 数据驱动的表格生成",
            "features", Arrays.asList("动态数据", "交替背景", "统计信息", "自适应列")
        ));
        
        response.put("basicExamples", basicExamples);
        
        // 业务场景示例
        Map<String, Object> businessExamples = new HashMap<>();
        businessExamples.put("employment-contract", Map.of(
            "url", "/business/employment-contract",
            "description", "员工入职合同 - HR业务场景",
            "parameters", Map.of(
                "employeeName", "员工姓名（默认：张三）",
                "position", "职位（默认：Java开发工程师）",
                "department", "部门（默认：技术部）",
                "salary", "薪资（默认：8000）"
            ),
            "example", "/business/employment-contract?employeeName=李四&position=前端工程师&salary=7500"
        ));
        
        businessExamples.put("sales-report", Map.of(
            "url", "/business/sales-report",
            "description", "月度销售报告 - 销售部门业务场景",
            "parameters", Map.of(
                "reportMonth", "报告月份（默认：2024年1月）"
            ),
            "example", "/business/sales-report?reportMonth=2024年2月"
        ));
        
        businessExamples.put("project-acceptance", Map.of(
            "url", "/business/project-acceptance",
            "description", "项目验收报告 - 项目管理业务场景",
            "parameters", Map.of(
                "projectName", "项目名称（默认：客户管理系统）",
                "clientName", "客户名称（默认：ABC公司）"
            ),
            "example", "/business/project-acceptance?projectName=ERP系统&clientName=XYZ公司"
        ));
        
        response.put("businessExamples", businessExamples);
        
        // 使用说明
        Map<String, Object> usage = new HashMap<>();
        usage.put("访问方式", "直接在浏览器中访问URL即可下载Word文档");
        usage.put("参数传递", "通过URL参数自定义内容，如：?employeeName=张三&salary=8000");
        usage.put("模板位置", "src/main/resources/templates/");
        usage.put("返回格式", "Word文档文件（.docx）");
        
        response.put("usage", usage);
        
        // 快速测试链接
        List<String> quickTests = Arrays.asList(
            "http://localhost:8080/basic-text",
            "http://localhost:8080/simple-table", 
            "http://localhost:8080/complex-table",
            "http://localhost:8080/list-example",
            "http://localhost:8080/business/employment-contract",
            "http://localhost:8080/business/sales-report"
        );
        response.put("quickTests", quickTests);
        
        return ResponseEntity.ok(response);
    }

    /**
     * 健康检查 - 验证所有服务是否正常
     */
    @GetMapping("/health-check")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        health.put("status", "UP");
        health.put("service", "POI-TL Examples Service");
        health.put("version", "1.0.0");
        health.put("timestamp", System.currentTimeMillis());
        
        // 检查各个功能模块
        Map<String, String> modules = new HashMap<>();
        modules.put("基础文本处理", "正常");
        modules.put("表格功能", "正常");
        modules.put("列表功能", "正常");
        modules.put("图片处理", "正常");
        modules.put("超链接功能", "正常");
        modules.put("业务场景示例", "正常");
        
        health.put("modules", modules);
        
        // 依赖检查
        Map<String, String> dependencies = new HashMap<>();
        dependencies.put("poi-tl", "已加载");
        dependencies.put("spring-boot-web", "已加载");
        dependencies.put("hutool", "已加载");
        
        health.put("dependencies", dependencies);
        
        return ResponseEntity.ok(health);
    }

    /**
     * 获取模板文件状态
     */
    @GetMapping("/template-status")
    public ResponseEntity<Map<String, Object>> getTemplateStatus() {
        Map<String, Object> status = new HashMap<>();
        
        status.put("title", "模板文件状态检查");
        status.put("location", "src/main/resources/templates/");
        
        // 需要的模板文件列表
        Map<String, String> requiredTemplates = new HashMap<>();
        requiredTemplates.put("template.docx", "原始表格示例模板");
        requiredTemplates.put("basic_text_template.docx", "基础文本模板");
        requiredTemplates.put("simple_table_template.docx", "简单表格模板");
        requiredTemplates.put("complex_table_template.docx", "复杂表格模板");
        requiredTemplates.put("list_template.docx", "列表模板");
        requiredTemplates.put("image_template.docx", "图片模板");
        requiredTemplates.put("hyperlink_template.docx", "超链接模板");
        requiredTemplates.put("comprehensive_report_template.docx", "综合报告模板");
        requiredTemplates.put("dynamic_table_template.docx", "动态表格模板");
        requiredTemplates.put("employment_contract_template.docx", "入职合同模板");
        requiredTemplates.put("sales_report_template.docx", "销售报告模板");
        requiredTemplates.put("project_acceptance_template.docx", "项目验收模板");
        
        status.put("requiredTemplates", requiredTemplates);
        
        // 创建说明
        status.put("createInstructions", "请参考'模板文件创建说明.md'文档创建对应的模板文件");
        status.put("templateGuide", "每个模板文件需要包含相应的占位符，如{{title}}、{{@table}}等");
        
        return ResponseEntity.ok(status);
    }

    /**
     * 获取开发指南
     */
    @GetMapping("/dev-guide")
    public ResponseEntity<Map<String, Object>> getDevGuide() {
        Map<String, Object> guide = new HashMap<>();
        
        guide.put("title", "POI-TL开发指南");
        
        // 核心概念
        Map<String, String> concepts = new HashMap<>();
        concepts.put("模板引擎", "poi-tl使用Word模板+数据模型的方式生成文档");
        concepts.put("占位符", "在Word模板中使用{{变量名}}定义占位符");
        concepts.put("数据绑定", "通过Map<String, Object>传递数据到模板");
        concepts.put("样式设置", "可以在代码中设置文本样式，也可以在模板中预设");
        
        guide.put("concepts", concepts);
        
        // 最佳实践
        List<String> bestPractices = Arrays.asList(
            "模板文件使用.docx格式，放在resources/templates目录",
            "占位符命名要清晰明确，与代码中的变量名保持一致",
            "复杂表格建议使用MergeCellRule处理单元格合并",
            "图片处理时注意设置合适的尺寸",
            "使用try-with-resources确保资源正确释放",
            "为不同业务场景创建专门的模板文件"
        );
        guide.put("bestPractices", bestPractices);
        
        // 常用API
        Map<String, String> commonApis = new HashMap<>();
        commonApis.put("Texts.of()", "创建文本数据，支持样式设置");
        commonApis.put("Tables.of()", "创建表格数据，支持边框和样式");
        commonApis.put("Rows.of()", "创建表格行数据");
        commonApis.put("Numberings.create()", "创建列表数据");
        commonApis.put("Pictures.ofLocal()", "插入本地图片");
        commonApis.put("XWPFTemplate.compile()", "编译模板");
        
        guide.put("commonApis", commonApis);
        
        return ResponseEntity.ok(guide);
    }
}
