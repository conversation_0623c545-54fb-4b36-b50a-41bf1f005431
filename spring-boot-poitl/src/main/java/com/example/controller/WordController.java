package com.example.controller;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;

import jakarta.annotation.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Word文档生成控制器
 * <p>
 * 该控制器提供Word文档生成功能，使用POI-TL模板引擎
 * 基于预定义的Word模板生成包含用户信息表格的文档
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
public class WordController {

    // 用于加载classpath下的Word模板文件
    @Resource
    private ResourceLoader resourceLoader;

    @GetMapping("/table")
    public ResponseEntity<StreamingResponseBody> generate() throws IOException {

        // 创建合并单元格的表头 - 必须也是5列
        RowRenderData mergedHeader = Rows.of("基本信息", "", "", "联系方式", "").textColor("FFFFFF")
                .bgColor("4472C4").center().create();

        // 创建表头行 - 5列
        RowRenderData header = Rows.of("姓名", "年龄", "性别", "手机号", "邮箱").center().create();

        // 创建数据行 - 5列
        RowRenderData row1 = Rows.of("张三", "28", "男", "13800138000", "<EMAIL>").center().create();
        RowRenderData row2 = Rows.of("李四", "30", "女", "13900139000", "<EMAIL>").center().create();

        List<RowRenderData> rows = new ArrayList<>();
        rows.add(mergedHeader);
        rows.add(header);
        rows.add(row1);
        rows.add(row2);

        TableRenderData table = Tables.of(rows.toArray(new RowRenderData[0])).border(BorderStyle.DEFAULT).create();

        MergeCellRule rule = MergeCellRule.builder()
                .map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(0, 2))
                .map(MergeCellRule.Grid.of(0, 3), MergeCellRule.Grid.of(0, 4)).build();

        /*MergeCellRule.MergeCellRuleBuilder builder = MergeCellRule.builder();
        builder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(0, 2));
        builder.map(MergeCellRule.Grid.of(0, 3), MergeCellRule.Grid.of(0, 4));
        MergeCellRule mergeCellRule = builder.build();*/
        table.setMergeRule(rule);


        // 创建表格
        Map<String, Object> data = new HashMap<>();
        data.put("table", table);


        // 2. 返回 StreamingResponseBody
        StreamingResponseBody body = out -> {
            // 模板流每次请求单独打开，用完即关 使用ResourceLoader加载模板
            try (InputStream templateStream =
                         resourceLoader.getResource("classpath:templates/template.docx").getInputStream();
                 XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data)) {

                // 直接写到响应输出流，无需 ByteArrayOutputStream
                template.write(out);
            }
        };

        String fileName = "word示例.docx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        ContentDisposition.attachment()
                                .filename("word示例.docx", StandardCharsets.UTF_8)  // ✅直接给中文
                                .toString())
                .body(body);
    }
}