package com.example.service;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.Style;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 基础文档操作服务
 * 基于Apache POI官方文档的基础功能实现
 * 
 * 参考文档：
 * - Apache POI官方文档: https://poi.apache.org/
 * - poi-tl官方文档: https://deepoove.com/poi-tl/
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class BasicDocumentService {

    @Resource
    private ResourceLoader resourceLoader;

    /**
     * 创建基础文本文档
     * 演示POI-TL的基础文本处理功能
     * 
     * 功能包括：
     * 1. 基础文本替换
     * 2. 文本样式设置（颜色、字体、大小）
     * 3. 段落格式设置
     * 4. 特殊字符处理
     */
    public byte[] createBasicTextDocument(String title, String content, String author) throws IOException {
        // 创建文档数据模型
        Map<String, Object> data = new HashMap<>();
        
        // 1. 基础文本替换
        data.put("documentTitle", title != null ? title : "POI-TL基础文本示例");
        data.put("documentContent", content != null ? content : "这是一个基础的文档内容示例。");
        data.put("documentAuthor", author != null ? author : "系统管理员");
        data.put("createDate", new Date());
        
        // 2. 带样式的文本
        TextRenderData titleText = Texts.of(title != null ? title : "POI-TL基础文本示例")
                .color("2F5597")  // 深蓝色
                .bold()           // 粗体
                .fontSize(18)     // 字体大小
                .create();
        data.put("styledTitle", titleText);
        
        // 3. 不同颜色的文本
        TextRenderData redText = Texts.of("红色文本").color("FF0000").create();
        TextRenderData greenText = Texts.of("绿色文本").color("00FF00").create();
        TextRenderData blueText = Texts.of("蓝色文本").color("0000FF").create();
        
        data.put("redText", redText);
        data.put("greenText", greenText);
        data.put("blueText", blueText);
        
        // 4. 不同字体样式的文本
        TextRenderData boldText = Texts.of("粗体文本").bold().create();
        TextRenderData italicText = Texts.of("斜体文本").italic().create();
        TextRenderData underlineText = Texts.of("下划线文本").underline().create();
        TextRenderData strikeText = Texts.of("删除线文本").strike().create();
        
        data.put("boldText", boldText);
        data.put("italicText", italicText);
        data.put("underlineText", underlineText);
        data.put("strikeText", strikeText);
        
        // 5. 不同字体大小的文本
        TextRenderData smallText = Texts.of("小字体文本").fontSize(10).create();
        TextRenderData normalText = Texts.of("正常字体文本").fontSize(12).create();
        TextRenderData largeText = Texts.of("大字体文本").fontSize(16).create();
        
        data.put("smallText", smallText);
        data.put("normalText", normalText);
        data.put("largeText", largeText);
        
        // 6. 组合样式文本
        TextRenderData combinedText = Texts.of("组合样式文本：粗体+斜体+红色+大字体")
                .bold()
                .italic()
                .color("FF0000")
                .fontSize(14)
                .create();
        data.put("combinedText", combinedText);
        
        // 使用模板渲染
        return renderTemplate("classpath:templates/basic_text_template.docx", data);
    }

    /**
     * 创建段落格式示例文档
     * 演示段落对齐、缩进等格式设置
     */
    public byte[] createParagraphFormatDocument() throws IOException {
        Map<String, Object> data = new HashMap<>();
        
        // 左对齐段落
        data.put("leftAlignText", "这是左对齐的段落文本。默认情况下，段落都是左对齐的。");
        
        // 居中对齐段落
        TextRenderData centerText = Texts.of("这是居中对齐的段落文本。")
                .center()
                .create();
        data.put("centerText", centerText);
        
        // 右对齐段落
        TextRenderData rightText = Texts.of("这是右对齐的段落文本。")
                .right()
                .create();
        data.put("rightText", rightText);
        
        // 两端对齐段落
        data.put("justifyText", "这是两端对齐的段落文本。两端对齐会调整单词间距，使每行文本都对齐到左右边距。");
        
        return renderTemplate("classpath:templates/paragraph_format_template.docx", data);
    }

    /**
     * 创建特殊字符处理示例文档
     * 演示特殊字符、换行符、制表符等的处理
     */
    public byte[] createSpecialCharacterDocument() throws IOException {
        Map<String, Object> data = new HashMap<>();
        
        // 包含换行符的文本
        data.put("multiLineText", "第一行文本\n第二行文本\n第三行文本");
        
        // 包含制表符的文本
        data.put("tabText", "姓名:\t张三\n年龄:\t28\n部门:\t技术部");
        
        // 包含特殊字符的文本
        data.put("specialChars", "特殊字符示例：©®™€£¥§¶†‡•…‰‹›""''–—");
        
        // HTML实体字符
        data.put("htmlEntities", "HTML实体：&lt; &gt; &amp; &quot; &apos;");
        
        // 数学符号
        data.put("mathSymbols", "数学符号：± × ÷ ≠ ≤ ≥ ∞ ∑ ∏ √ ∫");
        
        return renderTemplate("classpath:templates/special_char_template.docx", data);
    }

    /**
     * 创建文本格式综合示例
     * 展示各种文本格式的组合使用
     */
    public byte[] createComprehensiveTextDocument(Map<String, Object> customData) throws IOException {
        Map<String, Object> data = new HashMap<>();
        
        // 合并自定义数据
        if (customData != null) {
            data.putAll(customData);
        }
        
        // 文档标题
        TextRenderData docTitle = Texts.of("POI-TL文本格式综合示例")
                .color("2F5597")
                .bold()
                .fontSize(20)
                .center()
                .create();
        data.put("documentTitle", docTitle);
        
        // 章节标题
        TextRenderData chapterTitle = Texts.of("第一章：基础文本格式")
                .color("4472C4")
                .bold()
                .fontSize(16)
                .create();
        data.put("chapterTitle", chapterTitle);
        
        // 正文内容
        data.put("normalContent", "这是正常的正文内容。在实际应用中，我们经常需要对文本进行各种格式设置，以提高文档的可读性和美观性。");
        
        // 重点强调文本
        TextRenderData emphasisText = Texts.of("这是需要重点强调的内容")
                .color("FF0000")
                .bold()
                .fontSize(14)
                .create();
        data.put("emphasisText", emphasisText);
        
        // 引用文本
        TextRenderData quoteText = Texts.of("\"这是一段引用文本，通常使用斜体和特殊颜色来区分。\"")
                .italic()
                .color("666666")
                .create();
        data.put("quoteText", quoteText);
        
        // 代码文本（等宽字体效果）
        data.put("codeText", "public class Example { public static void main(String[] args) { System.out.println(\"Hello World\"); } }");
        
        // 脚注文本
        TextRenderData footnoteText = Texts.of("这是脚注文本¹")
                .fontSize(10)
                .color("888888")
                .create();
        data.put("footnoteText", footnoteText);
        
        return renderTemplate("classpath:templates/comprehensive_text_template.docx", data);
    }

    /**
     * 创建多语言文本示例
     * 演示中文、英文、数字等混合文本的处理
     */
    public byte[] createMultiLanguageDocument() throws IOException {
        Map<String, Object> data = new HashMap<>();
        
        // 中文文本
        data.put("chineseText", "这是中文文本示例：你好世界！");
        
        // 英文文本
        data.put("englishText", "This is English text example: Hello World!");
        
        // 数字文本
        data.put("numberText", "数字示例：123,456.789");
        
        // 混合文本
        data.put("mixedText", "混合文本示例：Hello 世界！Price: ¥123.45");
        
        // 日期时间格式
        data.put("dateTimeText", "日期时间：2024年1月1日 12:30:45");
        
        // 货币格式
        data.put("currencyText", "货币格式：￥1,234.56 / $123.45 / €98.76");
        
        return renderTemplate("classpath:templates/multi_language_template.docx", data);
    }

    /**
     * 渲染模板的通用方法
     * 
     * @param templatePath 模板路径
     * @param data 数据模型
     * @return 生成的文档字节数组
     * @throws IOException IO异常
     */
    private byte[] renderTemplate(String templatePath, Map<String, Object> data) throws IOException {
        try (InputStream templateStream = resourceLoader.getResource(templatePath).getInputStream();
             XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            template.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    /**
     * 获取基础文档操作的使用说明
     * 
     * @return 使用说明文档
     */
    public Map<String, Object> getBasicDocumentUsage() {
        Map<String, Object> usage = new HashMap<>();
        
        usage.put("title", "POI-TL基础文档操作使用说明");
        usage.put("description", "基于Apache POI官方文档的基础功能实现");
        
        Map<String, Object> features = new HashMap<>();
        features.put("textReplacement", "基础文本替换：使用{{变量名}}语法进行文本替换");
        features.put("textStyling", "文本样式设置：支持颜色、字体、大小、粗体、斜体等");
        features.put("paragraphFormat", "段落格式：支持对齐方式、缩进、行距等设置");
        features.put("specialCharacters", "特殊字符：支持换行符、制表符、特殊符号等");
        features.put("multiLanguage", "多语言支持：支持中文、英文、数字等混合文本");
        
        usage.put("features", features);
        
        Map<String, String> examples = new HashMap<>();
        examples.put("basicText", "基础文本替换和样式设置");
        examples.put("paragraphFormat", "段落格式设置示例");
        examples.put("specialCharacter", "特殊字符处理示例");
        examples.put("comprehensive", "综合文本格式示例");
        examples.put("multiLanguage", "多语言文本示例");
        
        usage.put("examples", examples);
        
        return usage;
    }
}
