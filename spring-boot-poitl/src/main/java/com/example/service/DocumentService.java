package com.example.service;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;
import com.deepoove.poi.data.style.Style;
import com.deepoove.poi.plugin.highlight.HighlightRenderPolicy;
import com.deepoove.poi.plugin.highlight.HighlightStyle;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 文档处理服务类
 * 基于Apache POI和poi-tl提供各种文档操作功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class DocumentService {

    @Resource
    private ResourceLoader resourceLoader;

    /**
     * 生成基础文本文档
     * 演示基本的文本替换和格式设置
     */
    public byte[] generateBasicTextDocument(Map<String, Object> data) throws IOException {
        InputStream templateStream = resourceLoader.getResource("classpath:templates/basic_text_template.docx").getInputStream();
        
        // 创建基础数据
        Map<String, Object> templateData = new HashMap<>();
        templateData.put("title", "POI-TL基础文本示例");
        templateData.put("author", data.getOrDefault("author", "系统管理员"));
        templateData.put("date", new Date());
        templateData.put("content", data.getOrDefault("content", "这是一个基础的文本内容示例。"));
        
        // 添加样式文本
        TextRenderData styledText = Texts.of("这是带样式的文本")
                .color("FF0000")
                .bold()
                .italic()
                .fontSize(14)
                .create();
        templateData.put("styledText", styledText);
        
        return renderTemplate(templateStream, templateData);
    }

    /**
     * 生成表格文档
     * 演示表格创建、样式设置和数据填充
     */
    public byte[] generateTableDocument(List<Map<String, Object>> tableData) throws IOException {
        InputStream templateStream = resourceLoader.getResource("classpath:templates/table_template.docx").getInputStream();
        
        Map<String, Object> templateData = new HashMap<>();
        
        // 创建简单表格
        TableRenderData simpleTable = createSimpleTable(tableData);
        templateData.put("simpleTable", simpleTable);
        
        // 创建复杂表格（带合并单元格）
        TableRenderData complexTable = createComplexTable();
        templateData.put("complexTable", complexTable);
        
        // 创建动态表格
        TableRenderData dynamicTable = createDynamicTable(tableData);
        templateData.put("dynamicTable", dynamicTable);
        
        return renderTemplate(templateStream, templateData);
    }

    /**
     * 生成图片文档
     * 演示图片插入和调整
     */
    public byte[] generateImageDocument(Map<String, Object> data) throws IOException {
        InputStream templateStream = resourceLoader.getResource("classpath:templates/image_template.docx").getInputStream();
        
        Map<String, Object> templateData = new HashMap<>();
        
        // 插入本地图片
        if (data.containsKey("imagePath")) {
            PictureRenderData picture = Pictures.ofLocal((String) data.get("imagePath"))
                    .size(200, 150)
                    .create();
            templateData.put("localImage", picture);
        }
        
        // 插入网络图片（如果提供URL）
        if (data.containsKey("imageUrl")) {
            PictureRenderData webPicture = Pictures.ofUrl((String) data.get("imageUrl"))
                    .size(300, 200)
                    .create();
            templateData.put("webImage", webPicture);
        }
        
        return renderTemplate(templateStream, templateData);
    }

    /**
     * 生成列表文档
     * 演示有序和无序列表
     */
    public byte[] generateListDocument(Map<String, Object> data) throws IOException {
        InputStream templateStream = resourceLoader.getResource("classpath:templates/list_template.docx").getInputStream();
        
        Map<String, Object> templateData = new HashMap<>();
        
        // 创建无序列表
        @SuppressWarnings("unchecked")
        List<String> items = (List<String>) data.getOrDefault("items", Arrays.asList("项目1", "项目2", "项目3"));
        NumberingRenderData unorderedList = Numberings.create(items.toArray(new String[0]));
        templateData.put("unorderedList", unorderedList);
        
        // 创建有序列表
        NumberingRenderData orderedList = Numberings.ofDecimal(items.toArray(new String[0])).create();
        templateData.put("orderedList", orderedList);
        
        return renderTemplate(templateStream, templateData);
    }

    /**
     * 生成高级功能文档
     * 演示书签、超链接等高级功能
     */
    public byte[] generateAdvancedDocument(Map<String, Object> data) throws IOException {
        InputStream templateStream = resourceLoader.getResource("classpath:templates/advanced_template.docx").getInputStream();
        
        Map<String, Object> templateData = new HashMap<>();
        
        // 创建超链接
        HyperlinkTextRenderData hyperlink = Texts.of("访问官网")
                .link("https://deepoove.com/poi-tl/")
                .create();
        templateData.put("hyperlink", hyperlink);
        
        // 创建代码高亮（需要插件支持）
        Map<String, Object> code = new HashMap<>();
        code.put("language", "java");
        code.put("code", "public class HelloWorld {\n    public static void main(String[] args) {\n        System.out.println(\"Hello, World!\");\n    }\n}");
        templateData.put("codeBlock", code);
        
        return renderTemplate(templateStream, templateData);
    }

    /**
     * 创建简单表格
     */
    private TableRenderData createSimpleTable(List<Map<String, Object>> data) {
        if (data == null || data.isEmpty()) {
            // 默认数据
            RowRenderData header = Rows.of("姓名", "年龄", "部门").textColor("FFFFFF").bgColor("4472C4").center().create();
            RowRenderData row1 = Rows.of("张三", "28", "技术部").center().create();
            RowRenderData row2 = Rows.of("李四", "30", "市场部").center().create();
            return Tables.of(header, row1, row2).border(BorderStyle.DEFAULT).create();
        }
        
        // 动态创建表格
        List<RowRenderData> rows = new ArrayList<>();
        
        // 创建表头
        if (!data.isEmpty()) {
            Set<String> keys = data.get(0).keySet();
            RowRenderData header = Rows.of(keys.toArray(new String[0]))
                    .textColor("FFFFFF")
                    .bgColor("4472C4")
                    .center()
                    .create();
            rows.add(header);
        }
        
        // 创建数据行
        for (Map<String, Object> row : data) {
            String[] values = row.values().stream()
                    .map(Object::toString)
                    .toArray(String[]::new);
            rows.add(Rows.of(values).center().create());
        }
        
        return Tables.of(rows.toArray(new RowRenderData[0])).border(BorderStyle.DEFAULT).create();
    }

    /**
     * 创建复杂表格（带合并单元格）
     */
    private TableRenderData createComplexTable() {
        // 创建合并单元格的表头
        RowRenderData mergedHeader = Rows.of("基本信息", "", "", "联系方式", "")
                .textColor("FFFFFF")
                .bgColor("4472C4")
                .center()
                .create();
        
        // 创建子表头
        RowRenderData subHeader = Rows.of("姓名", "年龄", "性别", "手机号", "邮箱")
                .textColor("FFFFFF")
                .bgColor("6C9BD1")
                .center()
                .create();
        
        // 创建数据行
        RowRenderData row1 = Rows.of("张三", "28", "男", "13800138000", "<EMAIL>").center().create();
        RowRenderData row2 = Rows.of("李四", "30", "女", "13900139000", "<EMAIL>").center().create();
        
        TableRenderData table = Tables.of(mergedHeader, subHeader, row1, row2).border(BorderStyle.DEFAULT).create();
        
        // 设置合并规则
        MergeCellRule rule = MergeCellRule.builder()
                .map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(0, 2))  // 合并"基本信息"
                .map(MergeCellRule.Grid.of(0, 3), MergeCellRule.Grid.of(0, 4))  // 合并"联系方式"
                .build();
        table.setMergeRule(rule);
        
        return table;
    }

    /**
     * 创建动态表格
     */
    private TableRenderData createDynamicTable(List<Map<String, Object>> data) {
        if (data == null || data.isEmpty()) {
            return createSimpleTable(null);
        }
        
        List<RowRenderData> rows = new ArrayList<>();
        
        // 动态表头
        Set<String> headers = data.get(0).keySet();
        RowRenderData headerRow = Rows.of(headers.toArray(new String[0]))
                .textColor("FFFFFF")
                .bgColor("2F5597")
                .center()
                .bold()
                .create();
        rows.add(headerRow);
        
        // 动态数据行，交替背景色
        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> rowData = data.get(i);
            String[] values = headers.stream()
                    .map(key -> String.valueOf(rowData.get(key)))
                    .toArray(String[]::new);
            
            RowRenderData dataRow = Rows.of(values).center().create();
            if (i % 2 == 1) {
                dataRow = Rows.of(values).bgColor("F2F2F2").center().create();
            }
            rows.add(dataRow);
        }
        
        return Tables.of(rows.toArray(new RowRenderData[0])).border(BorderStyle.DEFAULT).create();
    }

    /**
     * 渲染模板的通用方法
     */
    private byte[] renderTemplate(InputStream templateStream, Map<String, Object> data) throws IOException {
        try (XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            template.write(outputStream);
            return outputStream.toByteArray();
        } finally {
            templateStream.close();
        }
    }
}
