# POI-TL Word模板文件创建说明

## 概述

本文档说明如何为 WordController 中的各个示例创建对应的Word模板文件。所有模板文件应放置在 `src/main/resources/templates/` 目录下。

## 模板文件列表

### 1. basic_text_template.docx - 基础文本模板

**文件路径**: `src/main/resources/templates/basic_text_template.docx`

**模板内容**:
```
{{title}}

员工姓名：{{name}}
职位：{{position}}
生成日期：{{date}}

公司：{{company}}
网站：{{website}}

{{anchor}}
详细信息部分...
```

**标签说明**:
- `{{title}}` - 标题文本（带样式）
- `{{name}}` - 员工姓名
- `{{position}}` - 职位
- `{{date}}` - 日期
- `{{company}}` - 公司名称（带样式）
- `{{website}}` - 网站链接（超链接）
- `{{anchor}}` - 锚点文本

### 2. image_template.docx - 图片模板

**文件路径**: `src/main/resources/templates/image_template.docx`

**模板内容**:
```
公司Logo：
{{@logo}}

项目徽章：
{{@badge}}

用户头像：
{{@avatar}}
```

**标签说明**:
- `{{@logo}}` - 公司Logo图片
- `{{@badge}}` - 项目徽章图片
- `{{@avatar}}` - 用户头像图片

### 3. table_template.docx - 表格模板

**文件路径**: `src/main/resources/templates/table_template.docx`

**模板内容**:
```
简单表格：
{{#simple_table}}

样式表格：
{{#styled_table}}

合并单元格表格：
{{#merged_table}}
```

**标签说明**:
- `{{#simple_table}}` - 简单表格
- `{{#styled_table}}` - 带样式的表格
- `{{#merged_table}}` - 合并单元格的表格

### 4. list_template.docx - 列表模板

**文件路径**: `src/main/resources/templates/list_template.docx`

**模板内容**:
```
有序列表（数字编号）：
{{*ordered_list}}

无序列表（圆点）：
{{*bullet_list}}

字母编号列表：
{{*letter_list}}
```

**标签说明**:
- `{{*ordered_list}}` - 有序列表
- `{{*bullet_list}}` - 无序列表
- `{{*letter_list}}` - 字母编号列表

### 5. section_template.docx - 区块对模板

**文件路径**: `src/main/resources/templates/section_template.docx`

**模板内容**:
```
{{?show_details}}
详细信息：
用户姓名：{{user_info.name}}
部门：{{user_info.department}}
{{/show_details}}

员工列表：
{{?employees}}
{{_index}}. 姓名：{{name}} | 职位：{{position}} | 薪资：{{salary}} | 经验：{{experience}}
{{/employees}}

项目进度：
{{?projects}}
项目名称：{{name}}
状态：{{status}}
进度：{{progress}}%
{{?_is_last}}--- 项目列表结束 ---{{/}}
{{/projects}}
```

**标签说明**:
- `{{?show_details}}...{{/show_details}}` - 条件显示区块
- `{{?employees}}...{{/employees}}` - 员工列表循环
- `{{?projects}}...{{/projects}}` - 项目列表循环
- `{{_index}}` - 循环索引（从0开始）
- `{{_is_last}}` - 是否为最后一项

### 6. employee_report_template.docx - 员工报告模板

**文件路径**: `src/main/resources/templates/employee_report_template.docx`

**模板内容**:
```
{{report_title}}

员工照片：
{{@employee_photo}}

基本信息：
{{#basic_info}}

工作成果：
{{*achievements}}

技能评估：
{{#skill_table}}

报告日期：{{report_date}}
```

**标签说明**:
- `{{report_title}}` - 报告标题（带样式）
- `{{@employee_photo}}` - 员工照片
- `{{#basic_info}}` - 基本信息表格
- `{{*achievements}}` - 工作成果列表
- `{{#skill_table}}` - 技能评估表格
- `{{report_date}}` - 报告日期

### 7. contract_template.docx - 合同模板

**文件路径**: `src/main/resources/templates/contract_template.docx`

**模板内容**:
```
{{contract_title}}

合同编号：{{contract_no}}
签署日期：{{sign_date}}

甲方：深圳科技有限公司
乙方：{{client_name}}

合同金额：人民币{{amount}}元（大写：{{amount_chinese}}）

服务内容：
{{*services}}

付款计划：
{{#payment_schedule}}

甲方（盖章）：                    乙方（盖章）：

日期：{{sign_date}}              日期：{{sign_date}}
```

**标签说明**:
- `{{contract_title}}` - 合同标题
- `{{contract_no}}` - 合同编号
- `{{sign_date}}` - 签署日期
- `{{client_name}}` - 客户名称
- `{{amount}}` - 合同金额
- `{{amount_chinese}}` - 金额大写
- `{{*services}}` - 服务内容列表
- `{{#payment_schedule}}` - 付款计划表格

### 8. data_report_template.docx - 数据报表模板

**文件路径**: `src/main/resources/templates/data_report_template.docx`

**模板内容**:
```
{{report_title}}

报告期间：{{report_period}}
生成时间：{{generate_time}}

销售数据汇总：
{{#sales_table}}

区域销售分析：
{{?regions}}
{{rank}}. {{region}}：销售额{{sales}}元，占比{{percentage}}
{{/regions}}

总结与建议：
{{*summary}}
```

**标签说明**:
- `{{report_title}}` - 报表标题
- `{{report_period}}` - 报告期间
- `{{generate_time}}` - 生成时间
- `{{#sales_table}}` - 销售数据表格
- `{{?regions}}...{{/regions}}` - 区域数据循环
- `{{*summary}}` - 总结建议列表

### 9. api_doc_template.docx - API文档模板

**文件路径**: `src/main/resources/templates/api_doc_template.docx`

**模板内容**:
```
{{api_title}}

版本：{{version}}
基础URL：{{base_url}}
文档日期：{{doc_date}}

API接口列表：
{{?apis}}
{{method}} {{path}}
描述：{{description}}
权限：{{auth}}
{{/apis}}

请求参数：
{{#param_table}}

响应状态码：
{{*status_codes}}
```

**标签说明**:
- `{{api_title}}` - API文档标题
- `{{version}}` - 版本号
- `{{base_url}}` - 基础URL
- `{{doc_date}}` - 文档日期
- `{{?apis}}...{{/apis}}` - API列表循环
- `{{#param_table}}` - 参数表格
- `{{*status_codes}}` - 状态码列表

### 10. template.docx - 原有模板（兼容性）

**文件路径**: `src/main/resources/templates/template.docx`

**模板内容**:
```
用户信息表格：
{{#table01}}
```

**标签说明**:
- `{{#table01}}` - 用户信息表格

## 模板创建注意事项

### 1. 标签语法规则
- 文本标签：`{{变量名}}`
- 图片标签：`{{@变量名}}`
- 表格标签：`{{#变量名}}`
- 列表标签：`{{*变量名}}`
- 区块对：`{{?变量名}}...{{/变量名}}` 或 `{{?变量名}}...{{/}}`

### 2. 样式设置
- 标签的样式会应用到替换后的内容上
- 可以在模板中预设字体、颜色、大小等样式
- 表格可以预设边框、背景色等样式

### 3. 布局建议
- 推荐使用表格布局来设计专业的文档
- 可以使用文本框、页眉页脚等Word功能
- 注意页面边距和整体排版

### 4. 测试建议
- 创建模板后，先用简单数据测试
- 检查标签是否正确替换
- 验证样式和布局是否符合预期

## 快速开始

1. 使用Microsoft Word、WPS Office等软件创建Word文档
2. 在需要动态内容的位置插入对应的标签
3. 设置标签的样式（字体、颜色、大小等）
4. 保存为.docx格式
5. 放置到`src/main/resources/templates/`目录
6. 访问对应的API接口测试效果

## 示例访问地址

- 示例索引：http://localhost:8080/word/examples
- 基础文本：http://localhost:8080/word/basic-text
- 图片示例：http://localhost:8080/word/image-example
- 表格示例：http://localhost:8080/word/table-example
- 列表示例：http://localhost:8080/word/list-example
- 区块对示例：http://localhost:8080/word/section-example
- 员工报告：http://localhost:8080/word/employee-report
- 合同模板：http://localhost:8080/word/contract-example
- 数据报表：http://localhost:8080/word/data-report
- API文档：http://localhost:8080/word/api-doc

## 参考资源

- POI-TL官网：https://deepoove.com/poi-tl/
- Apache POI文档：https://poi.apache.org/
- Spring Boot官方文档：https://spring.io/projects/spring-boot
